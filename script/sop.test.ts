import { SopCsv } from '../admin_platform/app/api/sop_csv'
import { getUserId } from '../bot/config/chat_id'
import { Config } from '../bot/config/config'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { startTasks } from '../bot/service/moer/components/flow/schedule/task_starter'
import { loadConfigByWxId } from '../test/tools/load_config'
import { ActionType, Sop, TextType } from '../bot/service/moer/components/visualized_sop/visualized_sop_type'
import { writeFileSync } from 'node:fs'

describe('迁移sop', () => {
  jest.setTimeout(6000000)
  test('将所有sop的条件都附带一个fixed', async () => {
    const mongoClient = PrismaMongoClient.getInstance()
    const sops = await mongoClient.sop.findMany()
    for (const sop of sops) {
      for (let i = 0; i < sop.situations.length; i++) {
        for (let j = 0; j < sop.situations[i].conditions.length; j++) {
          sop.situations[i].conditions[j].type = 'fixed'
        }
      }
      await mongoClient.sop.update({ where:{ id:sop.id }, data:{ ...sop, id:undefined } as unknown as any })
    }
  })
  test('import sop', async () => {

    const sop = await SopCsv.parseCsv('./import.csv')
    const mongoClient = PrismaMongoClient.getInstance()
    await mongoClient.sop.createMany({ data:sop })
  })

  test('reset sop', async() => {
    const mongoClient = PrismaMongoClient.getInstance()
    const mongoConfigClient = PrismaMongoClient.getConfigInstance()
    const users = await mongoClient.chat.findMany({ where:{ course_no:71 } })
    const accounts = await mongoConfigClient.config.findMany({ where:{ enterpriseName:'moer' } })
    for (const account of accounts) {
      // console.log(account)
      // await clearTasks(users.map((item) => item.id), account.wechatId)
    }

    for (const user of users) {
      console.log(user.id)
      const userId = getUserId(user.id)
      Config.setting.wechatConfig = await loadConfigByWxId(user.wx_id)
      await startTasks(userId, user.id, true)
    }
  })

  test('export sop', async () => {
    const mongoClient = PrismaMongoClient.getInstance()
    const sops = (await mongoClient.sop.findMany() as Sop[]).filter((item) => item.tag == 'moer')
    let text = 'time_anchor,week,day,time,title,condition,action,enable\n'
    for (const sop of sops) {
      for (const situation of sop.situations) {
        let conditionText = ''
        for (let i = 0; i < situation.conditions.length; i++) {
          const condition = situation.conditions[i]
          if (i != 0) conditionText += '\n\n'
          conditionText += `${(condition.isOrNotIs ? '是' : '不是') + condition.condition  }`
        }
        let actionText = ''
        for (let i = 0; i < situation.action.length; i++) {
          const action = situation.action[i]
          if (i != 0) actionText += '\n\n'
          if (action.type === ActionType.text) {
            actionText += `文本: ${action.textList.map((item) => {
              if (item.type == TextType.fixed) {
                return item.text
              } else {
                return `{${item.tag}}`
              }
            }).join('') ?? ''}`
          } else if (action.type === ActionType.link) {
            actionText += `链接: ${action.description ?? ''}`
          } else if (action.type === ActionType.dynamicPrompt) {
            actionText += `动态提示: ${action.description ?? ''}`
          } else if (action.type === ActionType.image) {
            actionText += `图片: ${action.description ?? ''}`
          } else if (action.type === ActionType.video) {
            actionText += `视频: ${action.description ?? ''}`
          } else if (action.type === ActionType.voice) {
            actionText += `语音: ${action.description ?? ''}`
          } else if (action.type === ActionType.file) {
            actionText += `文件: ${action.description ?? ''}`
          } else if (action.type === ActionType.custom) {
            actionText += `自定义: ${action.tag ?? ''}`
          } else if (action.type === ActionType.videoChannel) {
            actionText += `视频号: ${action.description ?? ''}`
          } else {
            throw ('错误')
          }
        }
        text += `"${sop.time_anchor}","${sop.week}","${sop.day}","${sop.time}","${sop.title}","${conditionText}","${actionText}","${sop.enable}"\n`
      }
    }
    writeFileSync('sop_export.csv', text, { encoding: 'utf8' })
    console.log('已导出到 sop_export.csv')
  })
})