'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import Link from 'next/link'
import dayjs from 'dayjs'
import { IPlannerTaskWithUser } from '@/app/api/planner'
import relativeTime from 'dayjs/plugin/relativeTime'
dayjs.extend(relativeTime)


interface PlannerTaskListProps {
  getAllPlannerTasks: () => Promise<IPlannerTaskWithUser[]>
}

export function PlannerTaskList({ getAllPlannerTasks }: PlannerTaskListProps) {
  const [tasks, setTasks] = useState<IPlannerTaskWithUser[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    loadTasks()
  }, [])

  const loadTasks = async () => {
    try {
      setLoading(true)
      const plannerTasks = await getAllPlannerTasks()
      setTasks(plannerTasks)
    } catch (error) {
      toast.error('加载 planner 任务失败')
      console.error('Failed to load planner tasks:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredTasks = tasks.filter((task) =>
    task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.overall_goal.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'badge-warning'
      case 'completed':
        return 'badge-success'
      case 'failed':
        return 'badge-error'
      default:
        return 'badge-neutral'
    }
  }

  const getPriorityColor = (priority: number) => {
    if (priority >= 8) return 'text-error'
    if (priority >= 5) return 'text-warning'
    return 'text-success'
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Planner 任务管理</h1>
        <button
          className="btn btn-primary"
          onClick={loadTasks}
          disabled={loading}
        >
          刷新
        </button>
      </div>

      {/* 搜索框 */}
      <div className="mb-4">
        <input
          type="text"
          placeholder="搜索任务描述、用户名或目标..."
          className="input input-bordered w-full max-w-md"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* 统计信息 */}
      <div className="stats shadow mb-6">
        <div className="stat">
          <div className="stat-title">总任务数</div>
          <div className="stat-value">{tasks.length}</div>
        </div>
        <div className="stat">
          <div className="stat-title">筛选结果</div>
          <div className="stat-value">{filteredTasks.length}</div>
        </div>
        <div className="stat">
          <div className="stat-title">今日任务</div>
          <div className="stat-value">
            {tasks.filter((task) => dayjs(task.send_time).isSame(dayjs(), 'day')).length}
          </div>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="overflow-x-auto">
        <table className="table table-zebra w-full">
          <thead>
            <tr>
              <th>任务描述</th>
              <th>用户</th>
              <th>发送时间</th>
              <th>优先级</th>
              <th>状态</th>
              <th>总体目标</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {filteredTasks.map((task) => (
              <tr key={`${task.chat_id}-${task.id}`} className="hover">
                <td>
                  <div className="max-w-xs truncate" title={task.description}>
                    {task.description}
                  </div>
                </td>
                <td>
                  <Link
                    href={`/user/sop/${task.chat_id}`}
                    className="link link-primary font-medium"
                  >
                    {task.user_name}
                  </Link>
                  <div className="text-xs text-gray-500">{task.user_id}</div>
                </td>
                <td>
                  <div className="text-sm">
                    {dayjs(task.send_time).format('YYYY-MM-DD HH:mm')}
                  </div>
                  <div className="text-xs text-gray-500">
                    {dayjs(task.send_time).fromNow()}
                  </div>
                </td>
                <td>
                  <span className={`font-bold ${getPriorityColor(task.priority)}`}>
                    {task.priority}
                  </span>
                </td>
                <td>
                  <span className={`badge ${getStatusColor(task.status)}`}>
                    {task.status}
                  </span>
                </td>
                <td>
                  <div className="max-w-xs truncate" title={task.overall_goal}>
                    {task.overall_goal}
                  </div>
                </td>
                <td>
                  <div className="flex gap-2">
                    <Link
                      href={`/user/sop/${task.chat_id}`}
                      className="btn btn-sm btn-outline"
                    >
                      查看SOP
                    </Link>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredTasks.length === 0 && !loading && (
        <div className="text-center py-8">
          <p className="text-gray-500">没有找到匹配的任务</p>
        </div>
      )}
    </div>
  )
}
