'use client'
import { UserData } from '@/app/type/user'
import { useState } from 'react'
import { toast } from 'react-toastify'

export function ChatEdit<T>({
  chat,
  changeCourseNo,
  changeNextStage,
  changePhone,
  stageOption,
  updateIsPaid,
  clearCache
}: {
  chat:UserData,
  changeNextStage(chatId: string, stage: T): Promise<void>,
  changePhone(chatId: string, phone: string): Promise<void>,
  changeCourseNo(chatId: string, courseNo: number): Promise<void>,
  updateIsPaid(chatId:string, isPaid:boolean): Promise<void>
  clearCache(chatId:string):Promise<void>
  stageOption:string[]
}) {
  const [stage, setStage] = useState<string>(chat.chat_state.nextStage)
  const [phone, setPhone] = useState<string>(chat.phone ?? '')
  const [courseNo, setCourseNo] = useState<string> (`${chat.course_no ?? ''}`)
  const [loading, setLoading] = useState<boolean>(false)
  const [isPaid, setIsPaid] = useState<boolean>(chat.chat_state.state.is_complete_payment ?? false)

  return <div>
    <div className='text-2xl p-2'>编辑: {chat.contact.wx_name}</div>
    <form className='flex gap-2 items-center' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(changeNextStage(chat.id, stage as T), {
        pending: 'change next stage pending',
        success: 'change next stage success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-30'>切换阶段</label>
      <select className='select focus-within:outline-0' disabled={loading} value={stage} onChange={(e) => {
        setStage(e.currentTarget.value)
      }}>
        {stageOption.map((item, index) => {
          return <option key={index}>{item}</option>
        })}
      </select>
      <button type="submit" className='btn' disabled={loading}>切换</button>
    </form>
    <form className='flex gap-2 items-center mt-2' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(changePhone(chat.id, phone), {
        pending: 'change phone pending',
        success: 'change phone success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-30'>绑定手机号</label>
      <input type="text" className='input focus-within:outline-0' value={phone} onChange={(e) => { setPhone(e.currentTarget.value) }}/>
      <button type="submit" className='btn' disabled={loading}>绑定</button>
    </form>
    <form className='flex gap-2 items-center mt-2' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(changeCourseNo(chat.id, Number(courseNo)), {
        pending: 'change courseNo pending',
        success: 'change courseNo success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-30'>修改课程号</label>
      <input type="number" className='input focus-within:outline-0' value={courseNo} onChange={(e) => { setCourseNo(e.currentTarget.value) }}/>
      <button type="submit" className='btn' disabled={loading}>修改</button>
    </form>
    <div className='flex gap-2 items-center mt-2' >
      <label className='label w-40'>修改成单状态</label>
      <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isPaid} disabled={loading} onChange={(e) => {
        e.preventDefault()
        const checked = e.currentTarget.checked
        setLoading(true)
        toast.promise(async() => {
          await updateIsPaid(chat.id, e.currentTarget.checked)
          await clearCache(chat.id)
        }, {
          pending: 'change payment state pending',
          success: 'change payment state success',
          error: {
            render:(e) => {
              return `error: ${e.data}`
            }
          }
        }).then(() => {
          setIsPaid(checked)
        }).finally(() => {
          setLoading(false)
        })
      }}/>
    </div>
  </div>
}