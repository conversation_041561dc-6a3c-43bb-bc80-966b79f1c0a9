import { ITask } from '../../../../../bot/service/moer/components/visualized_sop/visualized_sop_type'
import Task from './task'

export async function UserExistSop({ queryExistSopByChatId, chatId } : {queryExistSopByChatId(chatId:string):Promise<ITask[]>, chatId:string}) {
  const jobs = await queryExistSopByChatId(chatId)
  return <div>
    <table className="table">
      <thead>
        <tr>
          <th>name</th>
          <th>sendTime</th>
          <th>action</th>
        </tr>
      </thead>
      <tbody>
        {jobs.map((item, index) => <Task key={index} task={item}/>)}
      </tbody>
    </table>
  </div>
}