import { getConditionJudgeKeys } from '@/app/api/sop'
import { queryAllTags } from '@/app/api/sop_tag'
import { querySopTopicByTag, changeSopTopicEnable, copyTopicToTag, deleteTopic, renameTopic, modifySopTopicCondition } from '@/app/api/sop_topic'
import { SopTopicShow } from '@/app/component/sop/sopTopicShow'

export default async function Page({ params }:{
  params: Promise<{ tag: string }>
}) {
  const { tag } = await params
  const decodeTag = decodeURIComponent(tag)
  return <div>
    <SopTopicShow
      tag={decodeTag}
      querySopTopicByTag={querySopTopicByTag}
      getConditionJudgeKeys={getConditionJudgeKeys}
      changeSopTopicEnable={changeSopTopicEnable}
      copyTopicToTag={copyTopicToTag}
      deleteTopic={deleteTopic}
      renameTopic={renameTopic}
      queryAllTags={queryAllTags}
      modifySopTopicCondition={modifySopTopicCondition}
    />
  </div>
}