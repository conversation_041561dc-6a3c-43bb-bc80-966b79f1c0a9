'use client'

import { useState } from 'react'
import { getAllPlannerTasks, IPlannerTaskWithUser } from '@/app/api/planner'
import { toast } from 'react-toastify'

export default function TestPage() {
  const [tasks, setTasks] = useState<IPlannerTaskWithUser[]>([])
  const [loading, setLoading] = useState(false)

  const testGetAllTasks = async () => {
    try {
      setLoading(true)
      const result = await getAllPlannerTasks()
      setTasks(result)
      toast.success(`成功获取 ${result.length} 个任务`)
    } catch (error) {
      toast.error('获取任务失败: ' + (error as Error).message)
      console.error('Test failed:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Planner API 测试</h1>
      
      <div className="mb-4">
        <button 
          className="btn btn-primary"
          onClick={testGetAllTasks}
          disabled={loading}
        >
          {loading ? '测试中...' : '测试获取所有任务'}
        </button>
      </div>

      {tasks.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-2">测试结果 ({tasks.length} 个任务):</h2>
          <div className="overflow-x-auto">
            <table className="table table-zebra w-full">
              <thead>
                <tr>
                  <th>任务ID</th>
                  <th>描述</th>
                  <th>用户</th>
                  <th>发送时间</th>
                  <th>状态</th>
                </tr>
              </thead>
              <tbody>
                {tasks.slice(0, 10).map((task, index) => (
                  <tr key={index}>
                    <td>{task.id}</td>
                    <td className="max-w-xs truncate">{task.description}</td>
                    <td>
                      {task.user_name}
                      <br />
                      <small className="text-gray-500">{task.user_id}</small>
                    </td>
                    <td>{new Date(task.send_time).toLocaleString()}</td>
                    <td>
                      <span className="badge badge-neutral">{task.status}</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {tasks.length > 10 && (
              <p className="text-center mt-2 text-gray-500">
                显示前 10 个任务，共 {tasks.length} 个
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
