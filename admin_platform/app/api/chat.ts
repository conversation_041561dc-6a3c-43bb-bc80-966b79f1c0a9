'use server'

import { chat } from '@prisma/client'
import axios from 'axios'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { UserData } from '../type/user'
import { MoerNode } from '../../../bot/service/moer/components/flow/nodes/type'
import { IChattingState, IUserSlot } from '../../../bot/service/moer/storage/chat_state_store'

export async function queryChats(nameOrPhone:string, courseNo?:number):Promise<UserData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const queryNameResult = await mongoClient.chat.findRaw({
    filter: {
      $and: [
        { course_no: courseNo },
        {
          $or: [
            { _id: nameOrPhone }, // MongoDB 的主键字段通常是 _id
            { 'contact.wx_name': { $regex: nameOrPhone, $options: 'i' } },
            { 'chat_state.userSlots.phoneNumber': nameOrPhone }
          ]
        }
      ]
    },
    options: {
      limit: 40,
      sort: {
        created_at: -1
      }
    }
  })
  if (!queryNameResult) {
    throw ('error')
  }
  const result = queryNameResult as unknown as any[]
  for (let i = 0; i < result.length; i++) {
    result[i].id = result[i]['_id']
  }

  return result.map((item) => transferChatIntoUserData(item))
}

export async function queryDefaultChats():Promise<UserData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:10,
    orderBy: {
      created_at: 'desc' // 按照 create_at 降序排列
    },
  })
  return queryNameResult.map((item) => transferChatIntoUserData(item))
}

export async function queryChatById(id:string):Promise<UserData | null> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.chat.findFirst({ where:{
    id
  } })
  if (result) {
    return transferChatIntoUserData(result)
  } else {
    return null
  }
}

export async function queryChatsWithoutAi(courseNo?:number): Promise<UserData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.chat.findMany({ where:{
    is_human_involved:true,
    course_no:courseNo
  },
  take:courseNo ? undefined : 50,
  orderBy:{
    created_at:'desc'
  }
  })
  return result.map((item) => transferChatIntoUserData(item))
}

export async function queryChatsWithoutPhone(courseNo?:number):Promise<UserData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.chat.findRaw({
    filter: { 'chat_state.userSlots.phoneNumber': { $exists: false }, course_no: courseNo },
    options: {
      limit: 40,
      sort: {
        created_at: -1
      }
    } }) as unknown as any[]
  for (let i = 0; i < result.length; i++) {
    result[i].id = result[i]['_id']
  }
  return (result as chat[]).map((item) => transferChatIntoUserData(item))
}

export async function changeIsHumanInvolved(chatId:string, isHumanInvolved:boolean) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_human_involved:isHumanInvolved } })
}

export async function changeIsStopGroupPush(chatId:string, isStopGroupPush:boolean) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_stop_group_push:isStopGroupPush } })
}

export async function changeCourseNo(chatId:string, courseNo:number) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ course_no:courseNo } })
}

export async function changePhone(chatId:string, phone:string) {
  throw ('not implement')
}

export async function changeNextStage(chatId:string, stage:MoerNode) {
  'use server'
  const mongoClient = PrismaMongoClient.getInstance()

  // 1. 获取聊天信息
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }

  // 2. 更新数据库中的 nextStage
  const updatedChatState = {
    ...chatInfo.chat_state,
    nextStage: stage
  }

  await mongoClient.chat.update({
    where: { id: chatId },
    data: {
      chat_state: updatedChatState as any
    }
  })

  await clearCache(chatId)
}

export async function updateIsPaid(chatId:string, isPaid:boolean):Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.$runCommandRaw({
    update: 'chat', // 集合名，注意区分大小写
    updates: [
      {
        q: { _id: chatId }, // 查询条件
        u: { $set: { 'chat_state.state.is_complete_payment': isPaid } }
      }
    ]
  })
}

export async function clearCache(chatId:string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }
  const mongoConfigInstance = PrismaMongoClient.getConfigInstance()
  const botInfo = await mongoConfigInstance.config.findFirst({
    select:{ address:true },
    where:{ wechatId:chatInfo.wx_id }
  })
  if (!botInfo) {
    throw '没有找到对应的机器人配置'
  }

  // 4. 调用客户端的 clear_cache 接口
  const address = botInfo.address
  try {
    const response = await axios.post(`${address}/clear_cache`, {
      chatId: chatId
    })

    if (response.data.code !== 200) {
      throw response.data.msg || '清除缓存失败'
    }
  } catch (error: any) {
    // 如果清除缓存失败，记录错误但不阻止操作
    console.error('清除缓存失败:', error.message || error)
    // 可以选择抛出错误或者只是警告
    // throw `更新成功但清除缓存失败: ${error.message || error}`
  }

}

export interface baseResponse {
  code:number
  msg:string
}

export async function getChatByCourseWeekRange(
  minCourseWeek: number,
  maxCourseWeek: number
) {
  const mongoClient = PrismaMongoClient.getInstance()
  const chatList = await mongoClient.chat.findMany({
    where: {
      course_no: {
        gte: minCourseWeek,
        lte: maxCourseWeek,
      },
    },
  })
  return chatList
}

function transferChatIntoUserData(chat:chat):UserData {
  return {
    id: chat.id,
    course_no: chat.course_no,
    contact: chat.contact,
    wx_id: chat.wx_id,
    is_human_involved: chat.is_human_involved,
    chat_state: {
      nextStage: chat.chat_state.nextStage,
      state:chat.chat_state.state as IChattingState
    },
    is_stop_group_push: chat.is_stop_group_push,
    phone: (chat.chat_state.userSlots as IUserSlot).phoneNumber ?? ''
  }
}