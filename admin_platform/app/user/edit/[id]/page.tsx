import { queryChatById, changeCourseNo, changeNextStage, changePhone, clearCache, updateIsPaid } from '@/app/api/chat'
import UserEdit from '@/app/component/user/edit'
import { MoerNode } from '../../../../../bot/service/moer/components/flow/nodes/type'

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = await params
  return <UserEdit
    id={param.id}
    queryChatById={queryChatById}
    changeCourseNo={changeCourseNo}
    changeNextStage={changeNextStage}
    changePhone={changePhone}
    stageOption={Object.values(MoerNode)}
    updateIsPaid={updateIsPaid}
    clearCache={clearCache}
  />
}



