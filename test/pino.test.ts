import pino from 'pino'
import { sleep } from '../bot/lib/schedule/schedule'
import { ChatDB } from '../bot/service/moer/database/chat'
import { SilentReAsk } from '../bot/service/moer/components/schedule/silent_requestion'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const transport = pino.transport({
      targets: [
        {
          target: 'pino-mongodb',
          options: {
            uri: 'mongodb://root:free1234$spirit!@dds-bp183f68818ca1d4-pub.mongodb.rds.aliyuncs.com:3717/admin',
            database: 'freespirit',
            collection: 'chat_log',
          },
          level: 'info',
        }
      ]
    })

    const logger = pino(transport)
    logger.info({ 'content': 'hello' })

    await sleep(1000 * 5)
  })

  it('1123', async () => {
    //@ts-ignore asdasd
    const chat = await ChatDB.getByMoerId(undefined)
    console.log(JSON.stringify(chat, null, 4))
  }, 60000)

  it('silentReask', async () => {
    await SilentReAsk.schedule('1', async () => {
      console.log('fku')
    }, 1000, {
      auto_retry: true,
      independent: true
    })

    await sleep(2000)
  }, 60000)
})