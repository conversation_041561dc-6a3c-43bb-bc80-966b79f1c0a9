import { Config } from '../bot/config/config'
import { loadConfigByAccountName } from './tools/load_config'
import { Queue } from 'bullmq'
import { RedisDB } from '../bot/model/redis/redis'
import { Sema } from 'async-sema'
import { DataService } from '../bot/service/moer/getter/getData'
import axios from 'axios'
import { HashSum } from '../bot/lib/hash/hash'
import { bindPhone } from '../bot_starter/helper/bindPhone'

describe('Test', function () {
  beforeAll(() => {

  })

  it('123213', async () => {
    const a = HashSum.hash(JSON.stringify({
      'chatId': '1688855694713983_R*****************',
      'userId': 'R:*****************',
      'name': '班会',
      'scheduleTime': {
        'is_course_week': false,
        'day': 7,
        'time': '20:00:00'
      },
      'sendTime': new Date('2025/1/1')
    }))

    const b = HashSum.hash(JSON.stringify({
      'chatId': '1688855694713983_R*****************',
      'userId': 'R:*****************',
      'name': '班会',
      'scheduleTime': {
        'is_course_week': false,
        'day': 7,
        'time': '20:00:00'
      },
      'sendTime': new Date('2025/1/1')
    }))

    console.log(a === b)
  }, 30000)

  it('mock payment', async () => {
    // 模拟付款事件
    // const userNames: string[] = []
    // for (const userName of userNames) {
    //
    // }
    const moerIds: number[] = [
      1084918
    ]

    for (const moerId of moerIds) {
      await axios.post('http://47.98.117.107:4001/moer/event', {
        'logid': 'goL9C0oXSL1cfbmynGcd',
        'transferNo': '2025052617091217482505523600-14',
        'sku': '**************',
        'userId': moerId,
        'event': 'course_pay_paid',
        'project': 'mainland'
      })
    }


  }, 60000)

  it('123123', async () => {
    console.log(new Date(*************).toLocaleString())
  }, 60000)

  it('消息积压', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('moer3')
    const queue = new Queue(Config.setting.wechatConfig?.id as string, { connection: RedisDB.getInstance() })

    console.log(JSON.stringify(await queue.getDelayed(), null, 4))
  }, 60000)

  it('new Date', async () => {
    console.log(new Date(************* + ********).toLocaleString())
  }, 60000)

  it('123123', async () => {
    console.log(process.env.MACHINE_ID)
  }, 60000)

  it('***********', async () => {
    const courseDate = await DataService.getCourseStartTime('7881303085027392_1688855025632783')
    console.log(courseDate.toLocaleString())
  }, 60000)

  it('delay', async () => {
    const delay = ********

    const totalSeconds = Math.floor(delay / 1000)
    const days = Math.floor(totalSeconds / 86400)
    let remainingSeconds = totalSeconds % 86400
    const hours = Math.floor(remainingSeconds / 3600)
    remainingSeconds %= 3600
    const minutes = Math.floor(remainingSeconds / 60)

    const timeParts: string[] = []
    if (days > 0) {
      timeParts.push(`${days}天`)
    }
    if (hours > 0) {
      timeParts.push(`${hours}小时`)
    }
    if (minutes > 0) {
      timeParts.push(`${minutes}分钟`)
    }

    if (timeParts.length === 0) {
      timeParts.push('不到1分钟')
    }

    if (delay < 0) {
      // logger.warn('Target time is in the past')
    } else {
      console.log(`in ${timeParts.join(' ')}`)
    }

    return delay
  }, 60000)


  it('should pass', async () => {
    const sema = new Sema(5) // 限制并发数为 5

    async function processTask(taskId: number) {
      await sema.acquire()
      try {
        console.log(`Task ${taskId} started`)
        await sleep(1000) // 模拟任务耗时 1 秒
      } finally {
        console.log(`Task ${taskId} finished`)
        await sema.acquire()
      }
    }

    function sleep(ms: number) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    }

    const start = Date.now()
    const taskCount = 10 // 计划执行 10 个任务
    const tasks = Array.from({ length: taskCount }, (_, i) => i + 1)

    // 创建包装函数，监测当前并发的任务数量
    async function monitoredTask(taskId: number) {
      await processTask(taskId)
    }

    // 执行所有任务并等待完成
    await Promise.all(tasks.map((taskId) => monitoredTask(taskId)))

    const duration = Date.now() - start

    // // 检查最大并发数量是否等于信号量的容量（5）
    // expect(maxConcurrentTasks).toBe(5)

    // 检查总时间是否在合理范围内
    // 因为每批最多 5 个任务，且每个任务耗时约 1000 ms，所以至少需要 2 秒（2000 ms）来完成 10 个任务
    expect(duration).toBeGreaterThanOrEqual(2000)
    expect(duration).toBeLessThan(3000) // 因为所有任务应该在 3 秒内完成
  }, 60000)

  it('34567890', async () => {
    await bindPhone({ chat_id: '7881299779900627_1688855694714276', phone: '15810292871' })
  }, 30000)
})