// import { <PERSON>r<PERSON><PERSON> } from '../bot/model/moer_api/moer'
// import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
// import { DataService } from '../bot/service/moer/getter/getData'
// import { Job, Queue } from 'bullmq'
// import { RedisDB } from '../bot/model/redis/redis'
// import { TaskName } from '../bot/service/moer/components/flow/schedule/type'
// import { getUserId } from '../bot/config/chat_id'
// import { ScheduleTask } from '../bot/service/moer/components/schedule/schedule'
// import { calTaskTime, IScheduleTime } from '../bot/service/moer/components/schedule/creat_schedule_task'
// import { ITask } from '../bot/service/moer/components/schedule/type'
// import { clearTasks, listSOPByChatId, startTasks } from '../bot/service/moer/components/flow/schedule/task_starter'
// import { Config, MoerAccountType } from '../bot/config/config'
// import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'
// import axios from 'axios'
// import { catchError } from '../bot/lib/error/catchError'
// import { NewCourseUser } from '../bot/service/moer/components/flow/helper/newCourseUser'
// import { JuziAPI } from '../bot/lib/juzi/api'
// import { CheckPreCourseCompletionTask } from '../bot/service/moer/components/flow/schedule/task/checkPreCourseCompletion'
// import { ChatStateStore, ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
// import { RedisCacheDB } from '../bot/model/redis/redis_cache'
// import logger from '../bot/model/logger/logger'
// import { SendWelcomeMessage } from '../bot/service/moer/components/flow/schedule/task/sendWelcomeMessage'
// import { sendEnergyTest } from '../bot/service/moer/components/flow/helper/sendEnergyTest'
// import { ExtractUserSlots } from '../bot/service/moer/components/flow/helper/slotsExtract'
// import { UUID } from '../bot/lib/uuid/uuid'
// import { LLM } from '../bot/lib/ai/llm/LLM'
// import { LLMNode } from '../bot/service/moer/components/flow/nodes/llm'
// import { getState } from '../bot/service/moer/components/flow/schedule/task/baseTask'
// import { fixDate } from '../bot/lib/date/mock'
// import { DateHelper } from '../bot/lib/date/date'
// import { OriginalDate } from '../bot/lib/date/jump'
// import { ChatDB } from '../bot/service/moer/database/chat'
// import { sleep } from '../bot/lib/schedule/schedule'
// import { FreeSpiritOCR } from '../bot/model/ocr/ocr'
// import { ClassGroupSend } from '../bot/service/moer/components/flow/schedule/task/classGroupSend'
// import { ClassGroupThirdCourseDayScript } from '../bot/service/moer/components/script/course_day3_group_script'
// import { DanmuHelper } from '../bot/service/moer/components/danmu/danmu'
// import { Client } from 'langsmith'
// import dayjs from 'dayjs'
// import isoWeek from 'dayjs/plugin/isoWeek'
// import { IWecomMsgType } from '../bot/lib/juzi/type'
// import { ClassGroupTaskManager } from '../bot_starter/client/class_group'
// import { ObjectUtil } from '../bot/lib/object'
// import { MoerEventForwardHandler } from '../bot_starter/handler/moer_event_forward'
// import { MoerNode } from '../bot/service/moer/components/flow/nodes/type'
// import { MessageSender } from '../bot/service/moer/components/message/message_send'
// import { HumanTransfer, HumanTransferType } from '../bot/service/moer/components/human_transfer/human_transfer'
// import { loadConfigByAccountName, loadConfigByWxId } from './tools/load_config'
// import { MemoryRecall } from '../bot/service/moer/components/memory/memory_search'
// import { JuziEvent } from '../bot_starter/handler/juzi_event'
//
// /**
//  * 墨尔控制台，查询各种参数
//  */
// describe('Test', function () {
//   beforeAll(() => {
//     Config.setting.wechatConfig = {
//       id: '****************',
//       botUserId: 'QiaoQiao',
//       name: '麦子老师',
//       notifyGroupId: 'R:*****************',
//       classGroupId: 'R:*****************',
//       courseNo: 38
//     }
//   })
//
//   it('sop', async () => {
//     // console.log(JSON.stringify(await JuziAPI.getCustomerInfo('****************', '****************'), null, 4))
//     const sops = await listSOPByChatId('7881303127991486_****************')
//
//     console.log(sops.length)
//   }, 60000)
//
//
//   it('fk', async () => {
//     // 跑一下今天进来的，所有没绑定手机号的人
//     const chats = await PrismaMongoClient.getInstance().chat.findMany({
//       where: {
//         created_at: {
//           gte: DateHelper.addDaysWithTime(new Date(), -1, 0, 0, 0)
//         }
//       }
//     })
//
//     Config.setting.wechatConfig = await loadConfigByAccountName('moer10')
//     const logs: string[] = []
//
//     for (const chat of chats) {
//       if (chat.moer_id) {
//         continue
//       }
//
//       Config.setting.wechatConfig.id = chat.wx_id
//
//       // 查一下手机号
//       const phone = await DataService.findPhoneNumber(chat.contact.wx_id)
//
//       const wechatAccountMapping = {
//         '****************': 'qiaoqiao',
//         '****************': 'tongtong',
//         '****************': 'moer3',
//         '****************': 'moer4',
//         '****************': 'moer5',
//         '****************': 'moer6',
//         '****************': 'moer7',
//         '****************': 'moer9',
//         '****************': 'moer10',
//         '****************': 'moer11',
//         '****************': 'moer12'
//       }
//
//       if (!phone) {
//         logs.push(`${wechatAccountMapping[chat.wx_id] ?? chat.wx_id} ${chat.contact.wx_name}`)
//       } else {
//         // 绑上 moer_id 和 期数
//         const moerUser = await MoerAPI.getUserByPhone(phone)
//         const courseNo = DataService.parseCourseNo(moerUser)
//
//         // 更新 客户标签
//         await DataService.updateTags(chat.contact.wx_id, `${courseNo}期`)
//
//         await ChatDB.updateMoerIdAndCourseNo(chat.id, moerUser.id.toString(), courseNo)
//
//         console.log('updated', chat.contact.wx_name, phone, moerUser.id.toString(), courseNo)
//       }
//     }
//
//     console.log(logs.join('\n'))
//   }, 60000)
//
//   it('JuziEvent', async () => {
//     Config.setting.wechatConfig = await loadConfigByAccountName('moer10')
//     await ChatStatStoreManager.initState('****************_****************')
//     Config.setting.localTest = false
//
//     await JuziEvent.handleFriendAcceptedEvent({
//       'imContactId': '****************',
//       'name': '皮卡丘',
//       'avatar': 'http://wx.qlogo.cn/mmhead/Q3auHgzwzM5Wn6Z5u54u0kVAsDMGfTNLdGO0QpBPu3RYFY4h5AepWA/0',
//       'gender': 1,
//       'createTimestamp': *************,
//       'imInfo': {
//         'externalUserId': 'asdasd',
//         'followUser': {
//           'wecomUserId': '2'
//         }
//       },
//       'botInfo': {
//         'botId': '67a9aa113dd078369deaba50',
//         'imBotId': '****************',
//         'name': '麦子10',
//         'avatar': 'https://wework.qpic.cn/wwpic3az/120913_5N73bMY7SJ-zWXS_1740228563/0'
//       }
//     })
//   }, 60000)
//
//   it('123e12dsa', async () => {
//     console.log(dayjs().isSame(dayjs('2025-05-04'), 'day'))
//   }, 60000)
//
//   it('debug', async () => {
//     const chatId = '7881301016041153_****************'
//
//     await ChatStatStoreManager.initState(chatId)
//
//     console.log(await DataService.isCompletedCourse(chatId, { day: 0 }))
//
//     // 完课礼只发一次
//     if (ChatStateStore.getFlags(chatId).is_send_pre_course_completion_gift || (await ChatHistoryService.getFormatChatHistoryByChatId(chatId)).includes('[冥想练习指南图片]')) {
//       return // 已经发送过小讲堂礼物
//     }
//
//     const currentTime = await DataService.getCurrentTime(chatId)
//
//
//     console.log(JSON.stringify(currentTime, null, 4))
//   }, 60000)
//
//   it('externalIdToWxId', async () => {
//     const externalIds:string[] =     ['wmXvL2CQAACt_VqG3qTof-qRNngQEuUQ', 'wmXvL2CQAAzkbO4imbNtZEPkZNbqS_Uw', 'wmXvL2CQAAsR5CAWoC945Sh9ANfTQJDQ', 'wmXvL2CQAAClqu8vy7dch7YLUN0_hPyg', 'wmXvL2CQAAZTHomZRnMvvkJlaDGAj6aQ', 'wmXvL2CQAAO0utqH6SmoKvVbhDqm-6vQ', 'wmXvL2CQAAmKE-2yg6jXuXJ5hljKkLCQ']
//
//
//     const res:string[]  = []
//     for (const externalId of externalIds) {
//       res.push(await JuziAPI.externalIdToWxId(externalId) as string)
//     }
//     console.log(res)
//
//   }, 60000)
//
//   it('补发欢迎语', async () => {
//     const users = [
//       {
//         'nickname': 'Amy陈',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881301092990147'
//       },
//       {
//         'nickname': 'A 星睿特精密轴承*HRCS*',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300428955906'
//       },
//       {
//         'nickname': '芳櫞',
//         'qiyu_userid': 'KongLing',
//         'wechat_id': '7881300491193790'
//       },
//       {
//         'nickname': '春暖花开',
//         'qiyu_userid': '109',
//         'wechat_id': '7881303255926165'
//       },
//       {
//         'nickname': '贝利亚',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881303163952901'
//       },
//       {
//         'nickname': '兴成五金石琼',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300825035868'
//       },
//       {
//         'nickname': '聂红斌',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302021025827'
//       },
//       {
//         'nickname': 'A蓉辉印刷设备有限公司18616723628',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302000050985'
//       },
//       {
//         'nickname': '吴工设计',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881302134934760'
//       },
//       {
//         'nickname': '欧石楠',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300290944036'
//       },
//       {
//         'nickname': '鬼都不信',
//         'qiyu_userid': 'BaoShu01',
//         'wechat_id': '7881303415927088'
//       },
//       {
//         'nickname': '东莞市久恒星李美英18823812869',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881301264919436'
//       },
//       {
//         'nickname': '女人世界服饰广场欢迎您',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301549036707'
//       },
//       {
//         'nickname': '幸福美妆13639351889',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301642946777'
//       },
//       {
//         'nickname': '。。。。。',
//         'qiyu_userid': 'BaoShuXiaoZhuShou_3',
//         'wechat_id': '7881300282917394'
//       },
//       {
//         'nickname': ' 西米皮',
//         'qiyu_userid': '2',
//         'wechat_id': '7881301288973079'
//       },
//       {
//         'nickname': 'Hala Madrid',
//         'qiyu_userid': 'MingXiangZhuJiao-LuoJiaLaoShi',
//         'wechat_id': '7881301940970286'
//       },
//       {
//         'nickname': 'Maggie Yin（英力士食品)',
//         'qiyu_userid': 'MaiZi',
//         'wechat_id': '7881301586913747'
//       },
//       {
//         'nickname': '只因有你…s',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881303047958302'
//       },
//       {
//         'nickname': 'Lilly',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881303458988657'
//       },
//       {
//         'nickname': '小白兔',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301240968699'
//       },
//       {
//         'nickname': '光明宝宝',
//         'qiyu_userid': '201',
//         'wechat_id': '7881300989140441'
//       },
//       {
//         'nickname': '玲玲',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302145057954'
//       },
//       {
//         'nickname': '陕西洛川苹果供应链【盛江果业】',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881299564913607'
//       },
//       {
//         'nickname': '海纳百川',
//         'qiyu_userid': 'AITEST1',
//         'wechat_id': '7881301192961416'
//       },
//       {
//         'nickname': 'edcee3000',
//         'qiyu_userid': 'BaoShu01',
//         'wechat_id': '7881303293106721'
//       },
//       {
//         'nickname': '李元清',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300847942731'
//       },
//       {
//         'nickname': '傲雪棋升级茹娣免费加盟',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881303240915945'
//       },
//       {
//         'nickname': '夏木',
//         'qiyu_userid': 'Aitest',
//         'wechat_id': '7881301619302751'
//       },
//       {
//         'nickname': '美好的明天',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881301666930848'
//       },
//       {
//         'nickname': 'A瓮洞海燕蛋糕店18375018373～杨',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300796935662'
//       },
//       {
//         'nickname': '千祥云集',
//         'qiyu_userid': '101',
//         'wechat_id': '7881301321023581'
//       },
//       {
//         'nickname': '郸匠装饰-张百万',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300299004779'
//       },
//       {
//         'nickname': '奇  龙',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300294906413'
//       },
//       {
//         'nickname': 'L号妖怪',
//         'qiyu_userid': 'KongLing',
//         'wechat_id': '7881301609928232'
//       },
//       {
//         'nickname': '水无形',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881299468922550'
//       },
//       {
//         'nickname': '怡发家具城，李超13907500813',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881299978922646'
//       },
//       {
//         'nickname': '黄立英',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300077918384'
//       },
//       {
//         'nickname': '张同学',
//         'qiyu_userid': '109',
//         'wechat_id': '7881299723017507'
//       },
//       {
//         'nickname': '庄诚',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881301341932512'
//       },
//       {
//         'nickname': '！！！',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302027185252'
//       },
//       {
//         'nickname': '芳',
//         'qiyu_userid': '2',
//         'wechat_id': '7881299732962954'
//       },
//       {
//         'nickname': '顺其自然',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881302319016645'
//       },
//       {
//         'nickname': '衣香丽影',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881299947033964'
//       },
//       {
//         'nickname': '海浪云彩民宿13056831186',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300441082642'
//       },
//       {
//         'nickname': 'A张勇丽15849045995',
//         'qiyu_userid': '201',
//         'wechat_id': '7881300153945201'
//       },
//       {
//         'nickname': '💍快乐人生💍',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881299877928270'
//       },
//       {
//         'nickname': '李娜',
//         'qiyu_userid': 'AITEST1',
//         'wechat_id': '7881300717972146'
//       },
//       {
//         'nickname': '唐学锋dp65768436',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881301462920725'
//       },
//       {
//         'nickname': '风云',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301924020939'
//       },
//       {
//         'nickname': '富贵人生13884102689',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881301855923105'
//       },
//       {
//         'nickname': '菜鸟驿站（同心路站，枣行社区站）',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300923919065'
//       },
//       {
//         'nickname': '贵人来',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300799029974'
//       },
//       {
//         'nickname': '欧普照明，畅13233236662',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881299701921078'
//       },
//       {
//         'nickname': 'meng',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300208049078'
//       },
//       {
//         'nickname': '陈得峰13461982368',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301654039752'
//       },
//       {
//         'nickname': '才让卓玛',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881302471988431'
//       },
//       {
//         'nickname': '高中任',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300859974395'
//       },
//       {
//         'nickname': '止语',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302438932762'
//       },
//       {
//         'nickname': 'WZP',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300224956572'
//       },
//       {
//         'nickname': '茹益',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881301626930574'
//       },
//       {
//         'nickname': 'AD.Xu（😍😍😍）',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881302212938649'
//       },
//       {
//         'nickname': '蒋堡廉15867639518',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300567979293'
//       },
//       {
//         'nickname': '阜宁小刀🛵 陈军',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881303342908446'
//       },
//       {
//         'nickname': '张俊',
//         'qiyu_userid': 'Aitest',
//         'wechat_id': '7881302413951567'
//       },
//       {
//         'nickname': '得失之见',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302774922137'
//       },
//       {
//         'nickname': '日月同辉（汤中明）',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881303495100071'
//       },
//       {
//         'nickname': '朝阳',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881299495026417'
//       },
//       {
//         'nickname': '莫语',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302212969665'
//       },
//       {
//         'nickname': '毛峻',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881299697954951'
//       },
//       {
//         'nickname': '琦',
//         'qiyu_userid': '101',
//         'wechat_id': '7881302104047058'
//       },
//       {
//         'nickname': '唐芳｜深度熊副院长｜企业教练🌀',
//         'qiyu_userid': 'KongLing',
//         'wechat_id': '7881301619920989'
//       },
//       {
//         'nickname': '珏',
//         'qiyu_userid': '109',
//         'wechat_id': '7881299913023569'
//       },
//       {
//         'nickname': '洪广勤中医骨科',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302850924956'
//       },
//       {
//         'nickname': '子洺',
//         'qiyu_userid': '2',
//         'wechat_id': '7881300952990692'
//       },
//       {
//         'nickname': '张氏素弘堂调理颈肩腰腿疼。（西）',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302140954709'
//       },
//       {
//         'nickname': '君🍀',
//         'qiyu_userid': 'MingXiangZhuJiao-LuoJiaLaoShi',
//         'wechat_id': '7881300906980689'
//       },
//       {
//         'nickname': '南海清风',
//         'qiyu_userid': 'MaiZi',
//         'wechat_id': '7881301978937521'
//       },
//       {
//         'nickname': '川鑫电器15982047106..',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300558052652'
//       },
//       {
//         'nickname': 'LUO卜',
//         'qiyu_userid': '201',
//         'wechat_id': '7881300569986240'
//       },
//       {
//         'nickname': 'A娜娜',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302213923507'
//       },
//       {
//         'nickname': '@@文洁全屋定制工厂(禹航路80号)',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300970003521'
//       },
//       {
//         'nickname': '老杨',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302659337511'
//       },
//       {
//         'nickname': '云卷云舒',
//         'qiyu_userid': 'AITEST1',
//         'wechat_id': '7881300875005655'
//       },
//       {
//         'nickname': '樱桃没有丸子～',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881301898976684'
//       },
//       {
//         'nickname': '回忆过去',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300954960860'
//       },
//       {
//         'nickname': '赵军',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302917943679'
//       },
//       {
//         'nickname': '阳谷乐佳动物医院',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881302567098121'
//       },
//       {
//         'nickname': '陈晓利',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302409058213'
//       },
//       {
//         'nickname': '小🐉山东烧烤',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301040943872'
//       },
//       {
//         'nickname': '杨浩',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881301073927059'
//       },
//       {
//         'nickname': '沐辰',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881301806963902'
//       },
//       {
//         'nickname': '胡开伟（道吾山食品）',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300500956908'
//       },
//       {
//         'nickname': '圣域星辰、',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881301218288991'
//       },
//       {
//         'nickname': '六月18058213132',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302551948914'
//       },
//       {
//         'nickname': '宁静致远',
//         'qiyu_userid': 'Aitest',
//         'wechat_id': '7881300137175947'
//       },
//       {
//         'nickname': '齐玲燕',
//         'qiyu_userid': '101',
//         'wechat_id': '7881301414020278'
//       },
//       {
//         'nickname': '吴曙光',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881301479916436'
//       },
//       {
//         'nickname': '唐震',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881303212911420'
//       },
//       {
//         'nickname': 'Cindy',
//         'qiyu_userid': 'KongLing',
//         'wechat_id': '7881302935921915'
//       },
//       {
//         'nickname': '吴寒',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881303148918439'
//       },
//       {
//         'nickname': '季眠^',
//         'qiyu_userid': 'BaoShuXiaoZhuShou_3',
//         'wechat_id': '7881301595230648'
//       },
//       {
//         'nickname': 'დ᭄💞⃟余生ꦾ随心💓ꪶꪫꪜꫀ',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881303166957775'
//       },
//       {
//         'nickname': '【LY】A君君（专业问题肌·抗衰）',
//         'qiyu_userid': '109',
//         'wechat_id': '7881300097007679'
//       },
//       {
//         'nickname': '珊珊',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300026050919'
//       },
//       {
//         'nickname': '陈玥君',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302729908696'
//       },
//       {
//         'nickname': '观众田瑞恒',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300383987421'
//       },
//       {
//         'nickname': '箫桐',
//         'qiyu_userid': 'MaiZi',
//         'wechat_id': '7881303387975791'
//       },
//       {
//         'nickname': '简单快乐～丫',
//         'qiyu_userid': '201',
//         'wechat_id': '7881302152926463'
//       },
//       {
//         'nickname': '东风',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881301750961941'
//       },
//       {
//         'nickname': '吉祥顺利1',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881301556032044'
//       },
//       {
//         'nickname': '齐子霄',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300650979122'
//       },
//       {
//         'nickname': 'ylj  🌹姚',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881303236924468'
//       },
//       {
//         'nickname': '八谢容',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302482998913'
//       },
//       {
//         'nickname': '赣州華盛石业有限公司13576892373',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302217941602'
//       },
//       {
//         'nickname': '智媛',
//         'qiyu_userid': 'AITEST1',
//         'wechat_id': '7881302860154706'
//       },
//       {
//         'nickname': '共创共建共享',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302238058402'
//       },
//       {
//         'nickname': '向琴',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302272074867'
//       },
//       {
//         'nickname': '🌾童盛行🌾🌾香姐，🌾🌾',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302687994812'
//       },
//       {
//         'nickname': '镇江善瑞整脊理疗19825820322王',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300935034016'
//       },
//       {
//         'nickname': '百盛服饰鞋业',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881299873094798'
//       },
//       {
//         'nickname': '湖北赟力工贸',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302762926964'
//       },
//       {
//         'nickname': '高艳丽13955263275',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881303273914082'
//       },
//       {
//         'nickname': '凌敢',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300450116954'
//       },
//       {
//         'nickname': '稳稳的幸福',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301856989621'
//       },
//       {
//         'nickname': '家乐木门代骐宁13573265001',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881302273001946'
//       },
//       {
//         'nickname': '新蕾书店得力文具',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300326054328'
//       },
//       {
//         'nickname': '福音',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300446912343'
//       },
//       {
//         'nickname': '葛永岩',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300185013515'
//       },
//       {
//         'nickname': '金鸿旭门业',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881303490983795'
//       },
//       {
//         'nickname': 'A0英山酷苟教育｜汪飞',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300747292391'
//       },
//       {
//         'nickname': '嘉铭',
//         'qiyu_userid': 'Aitest',
//         'wechat_id': '7881301628018208'
//       },
//       {
//         'nickname': '坦然',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302117987313'
//       },
//       {
//         'nickname': '瑞华丰利',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300362152077'
//       },
//       {
//         'nickname': '為茗',
//         'qiyu_userid': '101',
//         'wechat_id': '7881302439211925'
//       },
//       {
//         'nickname': '厦门台贸~黄文贵',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881301348916482'
//       },
//       {
//         'nickname': '林林',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881301637955278'
//       },
//       {
//         'nickname': '福慧如意',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302815978196'
//       },
//       {
//         'nickname': '佳梦莲健康家居',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301734961075'
//       },
//       {
//         'nickname': '曙光',
//         'qiyu_userid': 'KongLing',
//         'wechat_id': '7881301102282424'
//       },
//       {
//         'nickname': '心',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301897936149'
//       },
//       {
//         'nickname': '惜缘',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300868095819'
//       },
//       {
//         'nickname': '🐛张华🐓',
//         'qiyu_userid': '109',
//         'wechat_id': '7881303583958483'
//       },
//       {
//         'nickname': '鑫富友~武洋',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881303302909498'
//       },
//       {
//         'nickname': '彼岸兔子',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300873030463'
//       },
//       {
//         'nickname': '邱建洪15918787188',
//         'qiyu_userid': 'MingXiangZhuJiao-LuoJiaLaoShi',
//         'wechat_id': '7881299513918855'
//       },
//       {
//         'nickname': '江鹏忠',
//         'qiyu_userid': 'MaiZi',
//         'wechat_id': '7881302391970029'
//       },
//       {
//         'nickname': '杨志忠18785531788',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300163975987'
//       },
//       {
//         'nickname': '中医研究',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881299979925552'
//       },
//       {
//         'nickname': '松树',
//         'qiyu_userid': '201',
//         'wechat_id': '7881300565916058'
//       },
//       {
//         'nickname': '多财🌻🌻多亿🌻🌻',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300590024075'
//       },
//       {
//         'nickname': 'chen',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300524959972'
//       },
//       {
//         'nickname': '大帅',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302216925654'
//       },
//       {
//         'nickname': 'A品锋工具',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881301174969879'
//       },
//       {
//         'nickname': '雨曦美业文文，何家暴傅',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300336983468'
//       },
//       {
//         'nickname': 'YY',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881303245987099'
//       },
//       {
//         'nickname': '堇',
//         'qiyu_userid': 'AITEST1',
//         'wechat_id': '7881300320971246'
//       },
//       {
//         'nickname': '空气.热水搬运工，空气能，空调，',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301933911197'
//       },
//       {
//         'nickname': '谢家玲',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881300297230138'
//       },
//       {
//         'nickname': '春风依旧',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301772208852'
//       },
//       {
//         'nickname': '徐正英精品18980256599',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881299613931596'
//       },
//       {
//         'nickname': '溢溢',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881301445899604'
//       },
//       {
//         'nickname': '季凡博(季西军)',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881302892912634'
//       },
//       {
//         'nickname': '三春瑞视眼镜店',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881302718943046'
//       },
//       {
//         'nickname': '👘帝垣装饰谢家玲16683253331',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881301238988929'
//       },
//       {
//         'nickname': '安徽敏华：熊秀敏13395655065',
//         'qiyu_userid': 'QiYue',
//         'wechat_id': '7881300518916331'
//       },
//       {
//         'nickname': '幸福一生',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881300996959571'
//       },
//       {
//         'nickname': '天道酬勤',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881301590075467'
//       },
//       {
//         'nickname': 'A一天一卷13943397556',
//         'qiyu_userid': 'CaiLi01',
//         'wechat_id': '7881303594929888'
//       },
//       {
//         'nickname': '串烤天下烧烤店',
//         'qiyu_userid': 'LiuZiJian_3',
//         'wechat_id': '7881303399920427'
//       },
//       {
//         'nickname': '晴天313',
//         'qiyu_userid': 'Aitest',
//         'wechat_id': '7881301821945368'
//       },
//       {
//         'nickname': 'A榆淇子',
//         'qiyu_userid': '109',
//         'wechat_id': '7881303077036954'
//       },
//       {
//         'nickname': '祝福',
//         'qiyu_userid': '2',
//         'wechat_id': '7881300199900374'
//       },
//       {
//         'nickname': '耕读',
//         'qiyu_userid': '201',
//         'wechat_id': '7881299809010785'
//       },
//       {
//         'nickname': '铭.',
//         'qiyu_userid': 'BaoShuXiaoZhuShou_3',
//         'wechat_id': '7881302540110616'
//       },
//       {
//         'nickname': '椰子水',
//         'qiyu_userid': 'BaoShu01',
//         'wechat_id': '7881299547186830'
//       },
//       {
//         'nickname': '🍐',
//         'qiyu_userid': 'BaoShu01',
//         'wechat_id': '7881301881172499'
//       },
//       {
//         'nickname': 'ズーリピカルです',
//         'qiyu_userid': 'BaoShuXiaoZhuShou_3',
//         'wechat_id': '7881301172065883'
//       },
//       {
//         'nickname': '北.',
//         'qiyu_userid': 'BaoShu01',
//         'wechat_id': '7881301147154377'
//       },
//       {
//         'nickname': '金',
//         'qiyu_userid': 'AITEST1',
//         'wechat_id': '7881300992080040'
//       },
//       {
//         'nickname': 'Xpp.🐾',
//         'qiyu_userid': 'BaoShu01',
//         'wechat_id': '7881303324974929'
//       },
//       {
//         'nickname': '漫杉.',
//         'qiyu_userid': 'BaoShu01',
//         'wechat_id': '7881301189134544'
//       },
//       {
//         'nickname': '詹仁儒',
//         'qiyu_userid': 'Aitest',
//         'wechat_id': '7881302155082788'
//       },
//       {
//         'nickname': '梦里花落知多少',
//         'qiyu_userid': '101',
//         'wechat_id': '7881302367962752'
//       },
//       {
//         'nickname': '😲',
//         'qiyu_userid': 'BaoShuXiaoZhuShou_3',
//         'wechat_id': '7881300664217764'
//       },
//       {
//         'nickname': 'f2',
//         'qiyu_userid': 'BaoShuXiaoZhuShou_3',
//         'wechat_id': '7881302449347451'
//       },
//       {
//         'nickname': '5u',
//         'qiyu_userid': 'BaoShu01',
//         'wechat_id': '7881301302052615'
//       },
//       {
//         'nickname': '常家辉',
//         'qiyu_userid': 'BaoShu01',
//         'wechat_id': '7881302253248765'
//       },
//       {
//         'nickname': '6xin',
//         'qiyu_userid': 'BaoShu01',
//         'wechat_id': '7881299836048617'
//       },
//       {
//         'nickname': 'lsbb',
//         'qiyu_userid': 'BaoShuXiaoZhuShou_3',
//         'wechat_id': '7881300662055755'
//       },
//       {
//         'nickname': 'windflower🍒',
//         'qiyu_userid': 'BaoShuXiaoZhuShou_3',
//         'wechat_id': '7881299608966297'
//       },
//       {
//         'nickname': '亚丽｜晴川',
//         'qiyu_userid': '2',
//         'wechat_id': '7881302780020343'
//       },
//       {
//         'nickname': '心身关系长玉',
//         'qiyu_userid': 'KongLing',
//         'wechat_id': '7881302385350502'
//       },
//       {
//         'nickname': '明丽💯',
//         'qiyu_userid': '201',
//         'wechat_id': '7881302799022011'
//       },
//       {
//         'nickname': '《博爱德》',
//         'qiyu_userid': 'MaiZi',
//         'wechat_id': '****************'
//       },
//       {
//         'nickname': '小张卍',
//         'qiyu_userid': 'MingXiangZhuJiao-LuoJiaLaoShi',
//         'wechat_id': '****************'
//       },
//       {
//         'nickname': '👑福多多',
//         'qiyu_userid': '109',
//         'wechat_id': '****************'
//       },
//       {
//         'nickname': '聿',
//         'qiyu_userid': 'BaoShuXiaoZhuShou_3',
//         'wechat_id': '****************'
//       }
//     ]
//
//     const accountIdMap = new Map()
//
//     const configs = await PrismaMongoClient.getConfigInstance().config.findMany(
//       {}
//     )
//
//     for (const config of configs) {
//       accountIdMap.set(config.botUserId, config)
//     }
//
//
//     for (const user of users) {
//       // 根据 userId 反向查找 accountId
//       if (accountIdMap.has(user.qiyu_userid)) {
//         const config = accountIdMap.get(user.qiyu_userid)
//         const accountId = config.wechatId
//         const wxId = user.wechat_id
//
//         const chatId = `${wxId  }_${  accountId}`
//
//         if (config.enterPriseName !== 'moer') {
//           continue
//         }
//
//         const chat = await ChatDB.getById(chatId)
//         if (chat) {
//           logger.log(`chat ${chatId} already exists`, user.nickname)
//           if (!chat.contact.wx_name) {
//             // 把名字补上
//             await ChatDB.updateContact(chatId, wxId, user.nickname)
//           }
//           continue
//         } else {
//           logger.log(`chat ${chatId} not exists`, user.nickname)
//         }
//
//         // // await SendWelcomeMessage.send(wxId)
//         // await axios.post(`${config.address}/event`, {
//         //   imContactId: wxId,  // 传入的 imContactId
//         //   name: user.nickname,  // 假设一个名字
//         //   avatar: 'https://example.com/avatar.jpg',  // 假设一个头像URL
//         //   gender: 1,  // 假设是男性
//         //   createTimestamp: Date.now(),  // 当前时间戳
//         //   imInfo: {
//         //     externalUserId: 'externalUser123',  // 假设的外部客户ID
//         //     followUser: {
//         //       wecomUserId: 'wecomUser456'  // 假设的 WeCom 客户ID
//         //     }
//         //   },
//         //   botInfo: {
//         //     botId: 'bot123',  // 假设的 Bot ID
//         //     imBotId: 'imBot123',  // 假设的 IM Bot ID
//         //     name: 'Bot Name',  // 假设的 Bot 名字
//         //     avatar: 'https://example.com/bot-avatar.jpg'  // 假设的 Bot 头像
//         //   }
//         // })
//
//       }
//     }
//
//   }, 60000)
//
//
//   it('timStamp', async () => {
//     const wxIds = ['7881300403958214', '7881300407911791', '7881300178969788', '7881302199921431', '7881302968946362', '7881302763917117']
//     for (const wxId of wxIds) {
//       const chatId = `${wxId  }_` + '****************'
//
//       const chat = await ChatDB.getById(chatId)
//       if (chat) {
//         if (chat.moer_id) {
//           console.log('skip', chatId)
//         } else {
//           await ChatDB.deleteById(chatId)
//         }
//       }
//     }
//   }, 60000)
//
//   it('1231dsada', async () => {
//     console.log(Config.setting.wechatConfig = await loadConfigByWxId('****************'))
//
//     console.log(Config.setting.wechatConfig?.id)
//   }, 60000)
//
//   it('补发已付款', async () => {
//     const chat_ids: string[] = [ '7881299653969471_****************', '7881302941975732_****************']
//     // @ts-ignore fku
//     Config.setting.wechatConfig = await loadConfigByWxId('****************')
//
//     for (const chatId of chat_ids) {
//       const chat = await ChatDB.getById(chatId)
//
//       if (!chat) {
//         throw new Error('chat not found')
//       }
//
//       await handlePaid(chat)
//     }
//
//
//     async function handlePaid(chatUser: any) {
//       await ChatStatStoreManager.initState(chatUser.id)
//
//       // if (ChatStateStore.get(chatUser.id).state.is_complete_payment) {
//       //   return
//       // }
//
//       ChatStateStore.update(chatUser.id, {
//         state: {
//           is_complete_payment: true,
//         },
//         nextStage: MoerNode.PostSale,
//       })
//       const userId = getUserId(chatUser.id)
//
//       await DataService.saveChat(chatUser.id, userId)
//
//       await MessageSender.sendById({
//         chat_id: chatUser.id,
//         user_id: getUserId(chatUser.id),
//         ai_msg: '恭喜同学加入系统课，感恩精进，咱们收件地址是哪里，给咱们寄垫子。',
//       })
//
//       await HumanTransfer.transfer(chatUser.id, userId, HumanTransferType.PaidCourse, 'onlyNotify')
//     }
//
//
//   }, 60000)
//
//   it('***********', async () => {
//     console.log(ObjectUtil.enumValueToKey(MoerAccountType, Config.getAccountType()))
//   }, 60000)
//
//   it('test123', async () => {
//     await JuziAPI.addFriendByPhone({
//       imBotId: '****************',
//       phone: '***********',
//       hello: '我是您购买的冥想入门营的助教老师'
//     })
//   }, 60000)
//
//   it('getQueue', async () => {
//     const queue = new Queue('7881301728970550_1688856297674945', {
//       connection: RedisDB.getInstance()
//     })
//
//     console.log(JSON.stringify(await queue.getDelayed(), null, 4))
//   }, 60000)
//
//   it('queueTasks', async () => {
//     const queueName = ClassGroupTaskManager.getQueueName()
//     const queue = new Queue(queueName, {
//       connection: RedisDB.getInstance()
//     })
//
//     console.log(JSON.stringify(await queue.getDelayed(), null, 4))
//   }, 60000)
//
//   it('memories', async () => {
//     const memory = await MemoryRecall.memoryRecall('我跟对象吵架了', '7881301847025991_****************', {
//       similaritySearchReturnNumber: 2,
//       filter: { 'bool': { 'should':[{ 'term':{ 'metadata.chat_id': '7881301847025991_****************' } }] } },
//     })
//
//     console.log(memory)
//   }, 60000)
//
//   it('上课提醒', async () => {
//     console.log(await DataService.isPaidSystemCourse('7881299676145198_****************'))
//     // const queue = new Queue('7881299676145198_****************', {
//     //   connection: RedisDB.getInstance()
//     // })
//     //
//     // console.log(JSON.stringify(await queue.getJobs(), null, 4))
//   }, 60000)
//
//   it('time', async () => {
//     // 获取 syq
//     const chat_id = '7881300846030208_1688857863677407'
//     console.log(await ChatHistoryService.getFormatChatHistoryByChatId(chat_id))
//     await ChatHistoryService.moveToEnd(chat_id, '1')
//
//     console.log(await ChatHistoryService.getFormatChatHistoryByChatId(chat_id))
//
//
//   }, 60000)
//
//   it('job', async () => {
//     const queueName =  '7881301413991996_****************'
//     const queue = new Queue(queueName, {
//       connection: RedisDB.getInstance()
//     })
//
//     console.log(JSON.stringify(await queue.getDelayed(), null, 4))
//   }, 60000)
//
//   it('remove', async () => {
//     // 当期
//     const chats = await DataService.getChatsByCourseNo(61)
//
//     let i = 0
//     for (const chat of chats) {
//       // 如果 chat 里面包含英文字母
//       if (/[a-zA-Z]/.test(chat.id)) {
//         continue
//       }
//
//       const  queue = new Queue(chat.id, {
//         connection: RedisDB.getInstance()
//       })
//
//       await queue.obliterate({ force: true })
//       i++
//       console.log(i)
//     }
//   }, 1E8)
//
//   it('remove123123', async () => {
//     const queueName =  '****************_R10793804172546542'
//     const queue = new Queue(queueName, {
//       connection: RedisDB.getInstance()
//     })
//
//     const jobs = await queue.getJobs()
//
//     for (const job of jobs) {
//       const jobData = job.data as  ITask
//       if (jobData.scheduleTime && jobData.scheduleTime.is_course_week && jobData.scheduleTime.day === 1) {
//         console.log(JSON.stringify(jobData, null, 4))
//         // await job.remove()
//       }
//     }
//
//   }, 60000)
//
//   it('123123', async () => {
//     interface EventPayload {
//       logid: string
//       userId: number
//       mobile: string
//       wxId: string
//       event: string
//       transferNo: string
//       course_type: number
//       stage: number
//     }
//
//     const postEvent = async () => {
//       const url = 'http://localhost:4001/moer/event'
//       const payload: EventPayload = {
//         logid: 'GxAuuFNjAjqjm9ti8aLZ',
//         userId: 781966,
//         mobile: '17664159319',
//         wxId: '7881300846030208_1688854546332791',
//         event: 'xiaohongshu_order',
//         transferNo: '20250110210025651402518398-37',
//         course_type: 1,
//         stage: 50
//       }
//
//       try {
//         const response = await axios.post(url, payload)
//         console.log('Response:', response.data)
//       } catch (error: any) {
//         if (axios.isAxiosError(error)) {
//           console.error('Axios Error:', error.response?.data || error.message)
//         } else {
//           console.error('Unexpected Error:', error)
//         }
//       }
//     }
//
//     await postEvent()
//   }, 60000)
//
//   it('123123123', async () => {
//     const timestamp = 1744672442372
//     const date = new Date(timestamp)
//     console.log(date.toString())
//   }, 60000)
//
//   it('任务队列', async () => {
//     // const time = await DataService.getCurrentTime('7881302126907048_****************')
//     //
//     // console.log(JSON.stringify(time, null, 4))
//
//     const queue = new Queue('7881302126907048_****************', {
//       connection: RedisDB.getInstance()
//     })
//
//     const delayedJobs = await queue.getJobs(['completed'])
//
//     for (const delayedJob of delayedJobs) {
//       if (delayedJob.data.tag) {
//         console.log(JSON.stringify(delayedJob, null, 4))
//       }
//     }
//
//     await new CheckPreCourseCompletionTask().process({
//       'chatId': '7881302126907048_****************',
//       'userId': '7881302126907048',
//       'name': 'T+N任务',
//       'tag': 't_plus_1_07_14'
//     })
//
//     // console.log(JSON.stringify(delayedJobs, null, 4))
//
//     // const job = await queue.getJob('12')
//     // if (!job) {
//     //   return
//     // }
//     //
//     // await MoerProcessor.generalProcess(job)
//   }, 60000)
//
//   it('12321', async () => {
//     console.log(DataService.getCurrentWeekCourseNo())
//
//   }, 60000)
//
//   it('before', async () => {
//     console.log(await DataService.hasPurchasedIntroCourseBefore('994323'))
//   }, 60000)
//
//   it('123', async () => {
//     await JuziAPI.sendGroupMsg('1688858254705213', 'R:*****************', {
//       type: IWecomMsgType.Text,
//       text: `${Config.setting.wechatConfig?.name}: 测试下，别慌张`
//     })
//   }, 60000)
//
//   it('getCourseWeek', async () => {
//     function getCurrentWeekCourseNo(): number {
//       function getPeriod(date: Date): number {
//         const startDate = dayjs('2024-01-29') // 示例日期
//
//         dayjs.extend(isoWeek)
//
//         const now = dayjs(date)
//
//         // 将开始日期和当前日期对齐到各自的周一
//         const startOfStartWeek = startDate.startOf('isoWeek')
//         const startOfCurrentWeek = now.startOf('isoWeek')
//
//         // 计算两个日期之间相差的周数，并加1表示第1周
//         const weekNo =  startOfCurrentWeek.diff(startOfStartWeek, 'week')
//         return weekNo
//       }
//       return getPeriod(new Date('2024-12-30T00:00:00+08:00'))
//     }
//
//     console.log(getCurrentWeekCourseNo())
//   }, 60000)
//
//   it('123123123', async () => {
//     const currentCourseNo = DataService.getCurrentWeekCourseNo()
//     console.log(currentCourseNo)
//   }, 60000)
//
//   it('langsmith', async () => {
//     process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
//     process.env.LANGCHAIN_TRACING_V2 = 'true'
//     process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName
//
//     const client = new Client()
//     const runUrl = await client.getRunUrl({ runId: '6e77a52c-57b6-4474-acc0-5b6f6fda442c' })
//     console.log(runUrl)
//   }, 60000)
//
//   it('v4', async () => {
//     console.log(UUID.v4())
//   }, 60000)
//
//   it('测试 @所有人', async () => {
//     Config.setting.localTest = false
//     const messages = ClassGroupThirdCourseDayScript.class_meeting6_day3('')
//     await new ClassGroupSend().sendGroupMsg('R:*****************', messages)
//   }, 60000)
//
//   it('分数', async () => {
//     const pattern =  /(-?\d+)分/
//     const match = '老师我750分'.match(pattern)
//     if (match) {
//       const score = parseInt(match[1], 10)
//       console.log(score)
//     }
//   }, 60000)
//
//   it('图片 能量测评', async () => {
//     async function extractScore(text: string) {
//       try {
//         const pattern =  /(-?\d+)分\/\d+分/
//         const match = text.match(pattern)
//
//         if (!match) return null
//
//         let score = match[1]
//         if (score.length > 3) {
//           score = score.slice(-3)
//         }
//
//         const numberScore = Number(score)
//
//         // 多重验证
//         if (
//           !Number.isFinite(numberScore) || // 检查是否为有限数字
//             !Number.isInteger(numberScore) // 检查是否为整数
//         ) {
//           return null
//         }
//
//         return numberScore
//       } catch (error) {
//         return null
//       }
//     }
//
//     const imageOcrResult =  await FreeSpiritOCR.recognizeUrl('https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/ocr_test/0_a.jpg')
//     const text = imageOcrResult.map((item) => item.text).join('')
//     if (text.includes('能量频率层级测评')) {
//       // 如果能直接获取到分数，直接按照图片内的分数解读
//       const score = await extractScore(text)
//       console.log(score)
//     }
//   }, 60000)
//
//   it('拉取弹幕', async () => {
//     const queue = new Queue('pullDanmu', {
//       connection: RedisDB.getInstance()
//     })
//
//     await queue.obliterate({ force: true })
//     console.log(await queue.getDelayedCount())
//   }, 60000)
//
//   it('prompt 测试', async () => {
//     const res = await LLM.predict(`参考之前客户的需求和痛点：
// {
//     "meditation_experience": "有使用冥想应用的经验但未能保持持续练习",
//     "goals_and_needs": "寻求缓解工作压力和改善睡眠质量",
//     "pain_points": "经历工作压力且担心没有时间练习",
//     "practice_challenges": "难以保持持续的冥想练习"
// }
//
// 根据之前客户的需求和痛点, 提供后续21天系统课程对客户针对性带来的好处。
//
// 例如:
// "到此，我们全部直播课就结束了哦，之前沟通的咱们想 xxx，不知道咱们这几天学习下来感觉怎么样？"
// 输出以"到此，我们全部直播课就结束了哦"开头，以"不知道咱们这几天学习下来感觉怎么样？"结束。输出不要超过三句话，简短，清晰。`)
//
//     console.log(res)
//   }, 60000)
//
//   it('先导课完课', async () => {
//     const isCompleted = await DataService.isCompletedCourse('7881300860265733_****************', { day: 0 })
//
//     console.log(isCompleted)
//     // console.log(JSON.stringify(await DataService.getCourseInfoByCourseNo(43), null, 4))
//   }, 1E8)
//
//   it('测试客户解读', async () => {
//     await LLMNode.invoke({
//       state: await getState('7881302369134376_****************', '7881302369134376', '还有我的果园是是普通围墙高，门口是竹子做的缝隙，果树比较单一，就有很多黄色的橙子树，中间是高大的，挂着很多果实的文旦树，没有工人，没有想到去摘果，就看着挂着，有去松土施肥，冬天有去包裹树干，你们老师这怎么解读？'),
//       dynamicPrompt: `客户完成第二课中和财富果园冥想跟练，根据练习指引，客户会看到些画像，我们目标是需要帮助它完成的冥想内容的解读，对课堂内容吸收更好。
// （1）根据画面几个纬度（果园大门的颜色/材质/新旧，果树状态和果实品种/数量，果园的围栏，和果树的互动，秋天的行为和收获），秋天行为和四季的景象来进行详细解释。
// 按照客户提到的部分进行解释就好。
// （2）为客户总结下当前反馈出来财富卡点，可以后续多注意练习提升。
// （3）询问客户解读是否有启示或者不明白的地方，进一步解释。
//
// 参考下面画面对应的解读的文档：
// 高墙: 一面高墙通常象征着强烈的界限和隔离感。在财富和事业的隐喻中，高墙可能代表了一种对资产和知识产权的坚固保护。这种结构的个人或组织，可能特别重视隐私和安全性，采取额外的预防措施来防范外部的侵扰。这样做的同时，也可能减少了与外界的交流和合作机会，反映了一种可能源于过去经验的防御性心态。然而，这也说明了这个人对内部资源的高度控制和对外交流的严格筛选，他们可能只会在非常谨慎和信任的基础上与他人分享。建议：内在画面调整，下调成符合果园的围栏感度。保持对外界的开放与信任。\\n\\n无果实: 一棵无果实的树象征着失去或缺乏成果。在心理学中，这可能表示内心的空虚感或对未完成目 标的焦虑。梦见一棵无果实的树可能反映了个人的不安全感，或者感觉自己的努力没有得到认可。无果实的状态也可能象征着不育或无法达成期望的状态，提示你需要关注自我的成长和发展，可能需要更多的精神上的滋养和个人投资。建议：内在画面调整，养育并维护果树回到正常结果状态。找到财富变现渠道，多学习落地，与外界多互动，找到价值。\\n\\n果实数量 丰收: 大量的果实可能象征着丰富、充满活力的内在世界。它可能表明你正在经历一个成长和收获的阶段，生活中充满了机会和可能性。在梦境或幻想中，果实的丰富可能是对成功和成就的预期，或者是内心深处对物质和情感丰盛的期望。\\n\\n果实成熟度 成熟果实: 成熟的果实通常象征着成就、完整性和成熟的心理状态。在梦境或潜意识中，成熟的果实可能表示你的某个计划、关系或内在发展已经达到了一个圆满的阶段。这也可以是一个积极的信号，暗示着你已经准备好享受你的努力所带来的成果。未成熟果实：未成熟的果实可能代表了未完成的项目、未达成的目标或者内在成长的早期阶段。它可以象征着你认识到自己还有成长和学习的空间，或者是焦虑和急迫感，担心没有足够的时间来完成你的目标。过熟或腐烂的果实：过熟或腐烂的果实可能指出失去的机会、错过的时机或者是某种形式的忽视。它可能表明你内心深处对某些事情持有遗憾，或者是提醒你需要更加关注自己的内在需求和情感健康。\\n\\n果园中的果树繁茂但未收获: 这代表了潜在的机会和未实现的潜力。在职场上，这可能意味着你有很多好主意和大量潜力，但由于某种原因，你没有采取行动让这些想法变成现实。可能是因为缺乏自信、害怕失败，或者是没有足够的资源和支持。在财富管理上，这可能意味着你有一些财富增长的机会，但却因为不愿意承担风险或缺少相应知识而错过了利用它们的最佳时机。\\n\\n四季分明 春天播种夏天打理秋收冬藏: 财富循环很好。\\n\\n例如: \\n例1:客户看到的画面是：没有大门,小冠木围栏，主树不清晰，没有看到结果,四季常青。\\n\\n\\"咱们的画面还挺独特的，没有大门，可能意味着在获取财富的道路上没有明显的限制和阻碍，有着较为开放的态势。小冠木围栏为果园提供了一定的边界感，但是预示咱们更倾向于自然的增长方式。\\n四季常青的景象，显示出财富的发展具有相对稳定和持续的特点。\\"\\n\\"然而，从财富卡点的角度来看，可能存在以下一些情况：\\n1.缺乏明确方向：主树不清晰且没有看到结果，也许暗示在财富积累的过程中，您对核心的财富来源和成果缺乏明确的认知，可能需要进一步探索和确定。\\n2.防护力度较弱：小冠木围栏的防护相对较薄弱，可能意味着在财富保护方面存在一定的不足，容易受到外界因素的影响。\\n3.主动进取不足：没有大门的设定，虽然看似没有限制，但也可能反映出在追求财富时缺乏主动设置目标和规划的意识
//
// 例如:
// 例1:客户看到的画面是：没有大门,小冠木围栏，主树不清晰，没有看到结果,四季常青。
//
// "咱们的画面还挺独特的，没有大门，可能意味着在获取财富的道路上没有明显的限制和阻碍，有着较为开放的态势。小冠木围栏为果园提供了一定的边界感，但是预示咱们更倾向于自然的增长方式。
// 四季常青的景象，显示出财富的发展具有相对稳定和持续的特点。"
// "然而，从财富卡点的角度来看，可能存在以下一些情况：
// 1.缺乏明确方向：主树不清晰且没有看到结果，也许暗示在财富积累的过程中，您对核心的财富来源和成果缺乏明确的认知，可能需要进一步探索和确定。
// 2.防护力度较弱：小冠木围栏的防护相对较薄弱，可能意味着在财富保护方面存在一定的不足，容易受到外界因素的影响。
// 3.主动进取不足：没有大门的设定，虽然看似没有限制，但也可能反映出在追求财富时缺乏主动设置目标和规划的意识
// 总的来说，觉察是改变的开始。看到现状就离目标近一大步，后续慢慢练习，会让我们的果园越来越富足的。"
//
// 例2:客户看到画面：灰色钢铁门，没有围栏，苹果树很多，最大果树在中间，果园结了很多苹果，整个果园只有我一个人爬树上摘果，四季变化没看到，睡着了[捂脸][捂脸]
//
// “每个画面其实都在反映您和财富关系的一个侧面。咱们得画面充满着丰收的喜悦。
// 灰色钢铁门是一种坚固和稳定的象征，表明咱们对事业上采取的是坚决和果断的态度，对挑战不屈不挠，在财务决策上也比较有警觉性。个人边界感比较强。
// 众多的苹果树，尤其是最大的果树在中间且结了很多苹果，这象征着丰富的财富成果。
// 整个果园只有你一个人在爬树上摘果，显示出你在财富获取上的积极主动和独立性。”
//
// “然而，从财富卡点的角度来看，可能存在以下一些情况：
// 【1】缺乏防护意识：没有围栏，也许意味着在财富保护方面缺乏一定的警惕性，容易让财富面临潜在的风险。
// 【2】孤独前行隐患：只有自己在果园劳作，可能反映出在财富积累过程中，缺乏合作和团队支持，可能会限制财富的进一步扩大。
// "总的来说，你的冥想画面反映了你对财富、内心成长和秩序的追求。在未来一个月，你可以通过坚持冥想，提升自我的觉知能力，更好地了解"
//
// 客户看到的画面是: 还有我的果园是是普通围墙高，门口是竹子做的缝隙，果树比较单一，就有很多黄色的橙子树，中间是高大的，挂着很多果实的文旦树，没有工人，没有想到去摘果，就看着挂着，有去松土施肥，冬天有去包裹树干，你们老师这怎么解读？`,
//       noSplit: true,
//       noInterrupt: true,
//       chatHistoryRounds: 0
//     })
//   }, 60000)
//
//   it('结合痛点', async () => {
//     const res = await LLM.predict(`参考客户过去的痛点，经历和能量测评结果，询问客户的练习感受
// 客户客户槽位：
// "{
//   "meditation_experience": [
//       "以前没有学过"
//   ],
//   "goals_and_needs": [
//       "主要是改善睡眠质量"
//   ],
//   "pain_points": [
//       "失眠",
//       "担心有事儿参加不了课程"
//   ]
// }"
//
// 能量测评结果:
// "客户之前做的能量测评分数为 200，属于正能量的范围，已经很不错了"
//
// 红靴子冥想核心解决的帮助是:红靴子主要帮助提升觉知，提升专注力和能量的
//
// 结合客户痛点，询问客户练习感受。
// 例如："红靴子咱们练得感觉怎么样呀？看到咱们之前沟通过xxx（结合客户信息和痛点），还记得您之前的能量分数是xx。红靴子的练习正是为了帮助提升觉知力、专注力以及能量的，不知道在这些方面，您有没有感受到一些变化或者帮助呢？
//
// 以 "红靴子咱们练得感觉怎么样呀？" 开始回复， 以"不知道在这些方面，您有没有感受到一些变化或者帮助呢？"， 不要超过3句话`)
//
//     console.log(res)
//   }, 60000)
//
//   it('userSlots', async () => {
//     const chats = await PrismaMongoClient.getInstance().chat.findMany({
//       where: {
//         course_no: 42
//       }
//     })
//
//     // 看下客户槽位不为空的
//     for (const chat of chats) {
//       // 只对 对话轮次 >= 3 轮  进行提取
//       const userMessages = await ChatHistoryService.getUserMessages(chat.id)
//       if (userMessages.length < 3 || ['Horus', '麦子', 'SYQ'].includes(chat.contact.wx_name)) {
//         continue
//       }
//
//       await ChatStatStoreManager.initState(chat.id)
//
//       const userSlots = await ExtractUserSlots.extractUserSlots(userMessages.join('\n'), chat.id, {
//         chat_id: chat.id,
//         user_id: getUserId(chat.id),
//         // round_id: UUID.short()
//       })
//
//       console.log(chat.contact.wx_name, JSON.stringify(userSlots, null, 4))
//     }
//
//   }, 1E8)
//
//   it('获取en', async () => {
//     const score = await DataService.getEnergyTestScore('7881301024220611_****************')
//     console.log(score)
//   }, 60000)
//
//   it('queue Task', async () => {
//     const queueName = '7881302302182577_****************'
//     const queue = new Queue(queueName, {
//       connection: RedisDB.getInstance()
//     })
//     const waitingJobs: Job[] = await queue.getDelayed()
//
//     console.log(JSON.stringify(waitingJobs, null, 4))
//   }, 60000)
//
//   it('notSendFlag', async () => {
//     const redisCache = new RedisCacheDB('notSendGroupMsg')
//     const notSendGroupMsg = await redisCache.get()
//     console.log(notSendGroupMsg)
//   }, 60000)
//
//   it('发送能量测评', async () => {
//     Config.setting.localTest = false
//
//     // 已经完成先导课，没发能量测评的人，全部发一遍能量测评
//     const users = await PrismaMongoClient.getInstance().chat.findMany({
//       where: {
//         course_no: 42,
//         wx_id: {
//           in: ['****************', '****************']
//         }
//       }
//     })
//
//     const toAdd: string[] = []
//
//     for (const chat of users) {
//       if (chat.contact.wx_name === 'Horus') {
//         continue
//       }
//
//       await ChatStatStoreManager.initState(chat.id)
//       Config.setting.wechatConfig = {
//         id: '****************',
//         botUserId: 'QiaoQiao',
//         name: '麦子老师',
//         notifyGroupId: 'R:*****************',
//         classGroupId: 'R:*****************',
//         courseNo: 38
//       }
//       Config.setting.wechatConfig.id = chat.wx_id
//
//       // 查询是否完成先导课
//       const isCompleted = await DataService.isCompletedCourse(chat.id, { day: 0 })
//       if (isCompleted) {
//         const chatHistory = await ChatHistoryService.getFormatChatHistoryByChatId(chat.id)
//         if (!chatHistory.includes('https://jsj.top/f/W8ktas')) {
//           await sendEnergyTest(chat.id, getUserId(chat.id))
//           console.log(chat.contact.wx_name)
//         }
//       }
//
//     }
//
//     console.log(JSON.stringify(toAdd, null, 4))
//     console.log(toAdd.length)
//   }, 1E8)
//
//   it('test1', async () => {
//     const chat_id = '7881300846030208_****************'
//     await ChatHistoryService.clearChatHistory(chat_id)
//
//     const user_id = getUserId(chat_id)
//     const isCompleted = await DataService.isCompletedCourse('7881301047907394_1688854546332791', { day: 0 })
//     console.log(isCompleted)
//
//     // 催先导课测试
//     await new CheckPreCourseCompletionTask().process({
//       name: TaskName.CheckPreCourseCompletion,
//       chatId: chat_id,
//       userId: user_id,
//       scheduleTime: {
//         is_course_week: false,
//         day: 2,
//         time: '08:00:00'
//       }
//     })
//   }, 60000)
//
//   it('修改 chatState', async () => {
//     // 槽位计数
//     await ChatStatStoreManager.initState('7881303589923027_****************')
//
//     const slotCount = ChatStateStore.get('7881303589923027_****************').slotAskedCount
//     slotCount['is_attend_live_stream'] = 1
//
//     await DataService.saveChat('7881303589923027_****************', '7881303589923027')
//   }, 60000)
//
//   it('关闭所有AI', async () => {
//     const chats = await PrismaMongoClient.getInstance().chat.findMany({
//       where: {
//         course_no: 46
//       }
//     })
//
//     await Promise.all(chats.map(async (chat) => {
//       if (Config.isInternalMember(chat.contact.wx_id)) {
//         return
//       }
//       if (chat.id === '7881299785944651_****************') {
//         return
//       }
//
//       const isPaidSystemCourse = await DataService.isPaidSystemCourse(chat.id)
//       if (isPaidSystemCourse) {
//         return
//       }
//
//       await ChatDB.setHumanInvolvement(chat.id, false)
//     }))
//   }, 60000)
//
//   it('手机号', async () => {
//     const phoneNumber = await (new SendWelcomeMessage() as any).findPhoneNumberByExternalId('7881299878917196')
//
//     console.log(phoneNumber)
//   }, 60000)
//
//   it('测试期数', async () => {
//     const currentCourseNo = DataService.getNextWeekCourseNo()
//
//     console.log(currentCourseNo)
//
//     const courseInfo = await MoerAPI.getCurrentCourseInfo(currentCourseNo)
//     console.log(JSON.stringify(courseInfo, null, 4))
//   }, 60000)
//
//   it('太酷了', async () => {
//     console.log('拉取弹幕...')
//
//     const currentCourseNo = DataService.getCurrentWeekCourseNo()
//     // 获取 直播 ID
//     const courseInfo = await DataService.getCourseInfoByCourseNo(currentCourseNo)
//     const liveId = courseInfo.resource.find((resource) => resource.day === 1)?.liveId as number
//
//     if (!liveId) {
//       logger.warn('获取直播 ID 失败')
//       return
//     }
//
//     // 批量拉取弹幕存储到数据库
//     // const formattedDate = dayjs().format('YYYY-MM-DD')
//     await DataService.pullLiveDanmuAndStore(liveId.toString(10), '2024-11-04', '2024-11-04', 99, 1)
//   }, 60000)
//
//   it('213123', async () => {
//     console.log(JSON.stringify(await DataService.getCourseInfoByCourseNo(40), null, 4))
//   }, 60000)
//
//   it('ceshi', async () => {
//     const groupId = 'R:10835076023607109'
//     // @ts-ignore fku
//     console.log(groupId.replaceAll(':', ''))
//     // @ts-ignore fku
//     const queueName = `classGroupTask_${groupId.replaceAll(':', '')}`
//     const queue = new Queue(queueName, {
//       connection: RedisDB.getInstance()
//     })
//
//     console.log(JSON.stringify(await queue.getJobs(), null, 4))
//   }, 60000)
//
//   it('cache', async () => {
//     // streamlit 加个按钮， 把下面的 redis key 设为 true
//
//     const redisCache = new RedisCacheDB('notSendGroupMsg')
//
//     // const notSendGroupMsg = await redisCache.get()
//
//     await redisCache.set('false')
//   }, 60000)
//
//   it('test123', async () => {
//     console.log(JSON.stringify(await DataService.getCourseLinkByCourseNo(40, 0), null, 4))
//   }, 60000)
//
//   it('redis', async () => {
//     // await RedisDB.getInstance().set('id1', JSON.stringify({ name: '123' }))
//     await PrismaMongoClient.getInstance().chat.deleteMany({
//       where: {
//         moer_id: '781966',
//       }
//     })
//   }, 60000)
//
//   it('asdad', async () => {
//     // 定义接口类型
//     interface ClassifyInput {
//       text: string
//     }
//
//     interface ClassifyRequest {
//       model?: string
//       classifier_id?: string
//       input: ClassifyInput[]
//       labels?: string[]
//     }
//
//     interface Classification {
//       object: string
//       index: number
//       prediction: string
//       score: number
//     }
//
//     interface ClassifyResponse {
//       usage: {
//         total_tokens: number
//       }
//       data: Classification[]
//     }
//
//     // API配置
//     const API_TOKEN = 'jina_f76da28f88eb48c98188be1a61da38d1ulqobHyGm2-RyIISB-DpziQkyz_y'
//     const API_URL = 'https://api.jina.ai/v1/classify'
//
//     // 测试函数
//     async function testClassification() {
//       try {
//         const inputs = [
//           '老师，我测完了，328分',
//           '灰色钢铁门，果园里面有很多苹果树，四季常青',
//           '老师，想问问后续还有什么课程可以学习',
//           '请问今晚几点开始上课？',
//           '能发一下课程资料吗？',
//           '老师辛苦了，谢谢指导'
//         ]
//
//         const requestData: ClassifyRequest = {
//           classifier_id: '7f4ea263-3879-4f39-8cb2-67e1efb0a9d4',
//           input: inputs.map((text) => ({ text }))
//         }
//
//         const response = await axios.post<ClassifyResponse>(
//           API_URL,
//           requestData,
//           {
//             headers: {
//               'Content-Type': 'application/json',
//               'Authorization': `Bearer ${API_TOKEN}`
//             }
//           }
//         )
//
//         // 打印分类结果
//         console.log('总Token消耗:', response.data.usage.total_tokens)
//         console.log('\n分类结果:')
//
//         const resultsArray = response.data.data.map((result) => {
//           return {
//             输入文本: inputs[result.index],
//             预测类别: result.prediction,
//             置信度: `${(result.score * 100).toFixed(2)}%`
//           }
//         })
//
//         console.log(JSON.stringify(resultsArray, null, 2))
//
//       } catch (error) {
//         console.error('发生错误:', error)
//       }
//     }
//
//     // 运行测试
//     await testClassification()
//   }, 60000)
//
//   it('train', async () => {
//
//     async function trainModel() {
//       const url = 'https://api.jina.ai/v1/train'
//       const apiKey = 'jina_f76da28f88eb48c98188be1a61da38d1ulqobHyGm2-RyIISB-DpziQkyz_y' // 替换为你的API密钥
//
//       const data = {
//         model: 'jina-embeddings-v3',
//         access: 'private',
//         num_iters: '10',
//         input: [
//           // 能量测评解读
//           { text: '老师，我测完了，328分', label: '能量测评解读' },
//           { text: '60分，怎么办，老师可以帮忙看看怎么回事吗？', label: '能量测评解读' },
//           { text: '435分，老师可以帮忙解读下吗？', label: '能量测评解读' },
//
//           // 财富果园解读
//           { text: '没有大门,小冠木围栏，主树不清晰，没有看到结果,四季常青', label: '财富果园解读' },
//           { text: '灰色钢铁门，没有围栏，苹果树很多，最大果树在中间，果园结了很多苹果，整个果园只有我一个人爬树上摘果，四季变化没看到，睡着了', label: '财富果园解读' },
//
//           // 课程销售场景
//           { text: '老师后续还有什么课程可以学习？', label: '课程销售场景' },
//           { text: '我看群里是有老师的课程可以报是吗', label: '课程销售场景' },
//
//           // 课程时间查询
//           { text: '第一节课什么时候上？', label: '课程时间查询' },
//           { text: '今晚几点开始？', label: '课程时间查询' },
//           { text: '这个课程是明天上吗？', label: '课程时间查询' },
//           { text: '沉浸式秒睡不是明天上吗？', label: '课程时间查询' },
//
//           // 发送课程资料
//           { text: '咱们这几天的课程安排是什么呢', label: '发送课程资料' },
//           { text: '发一下奖励，谢谢', label: '发送课程资料' },
//           { text: '老师我想要完课礼', label: '发送课程资料' },
//           { text: '请问有课程回放吗，今天没时间上课', label: '发送课程资料' },
//
//           // 正常对话聊天
//           { text: '谢谢老师', label: '正常对话聊天' },
//           { text: '好的', label: '正常对话聊天' },
//           { text: '感受很好，感觉像擎天柱一样在填上', label: '正常对话聊天' },
//         ]
//       }
//
//       try {
//         const response = await axios.post(url, data, {
//           headers: {
//             'Content-Type': 'application/json',
//             'Authorization': `Bearer ${apiKey}`
//           }
//         })
//         console.log('Training response:', response.data)
//       } catch (error) {
//         console.error('Error during training:', error)
//       }
//     }
//
//     await trainModel()
//   }, 60000)
//
//   it('21313', async () => {
//     // console.log(new Date().getHours())
//     await ChatStatStoreManager.initState('7881302600013465_****************')
//
//     console.log(await DataService.isInClass('7881302600013465_****************', { day: 1 }))
//   }, 60000)
//
//   it('21312', async () => {
//     console.log(await ChatHistoryService.getFormatChatHistoryByChatId('7881299791948506_****************'))
//     // const res = await PrismaMongoClient.getInstance().chat_history.deleteMany({
//     //   where: {
//     //     id: {
//     //       in: ['6718be72ea961ae1e5c6607c', '6718bfada505c762e7d059d9', '6718bfed99f4bdaf91167f17', '6718c04ac17cd92013718091' ] // 使用 `in` ��作符匹配多个 ID
//     //     }
//     //   }
//     // })
//     //
//     //
//     // console.log(JSON.stringify(res, null, 4))
//   }, 60000)
//
//   it('pullDanmu', async () => {
//     const queue = new Queue('pullDanmu', {
//       connection: RedisDB.getInstance()
//     })
//
//     console.log(await queue.getJobs())
//   }, 60000)
//
//   it('getCustomerInfo', async () => {
//     const userInfo = await JuziAPI.getCustomerInfo('****************', '7881302000913847')
//     console.log(userInfo)
//   }, 60000)
//
//   it('addTasks', async () => {
//     await startTasks('7881302158912289', '7881302158912289_****************')
//   }, 60000)
//
//   it('danmu', async () => {
//     await DanmuHelper.addNextWeekPullDanmuTasks()
//   }, 60000)
//
//   it('getCourseINfo', async () => {
//     const queue = new Queue('pullDanmu', {
//       connection: RedisDB.getInstance()
//     })
//
//     const hasTasks = await queue.getJobs()
//     console.log(JSON.stringify(hasTasks, null, 4))
//     // console.log(JSON.stringify(await DataService.getCourseInfoByCourseNo(46), null, 4))
//   }, 60000)
//
//   it('获取客户课程', async () => {
//     console.log(JSON.stringify(await MoerAPI.getUserCourses('1072777'), null, 4))
//   }, 60000)
//
//   it('李娟', async () => {
//     const moerUser = await MoerAPI.getUserByPhone('13646395182')
//     const courseNo = DataService.parseCourseNo(moerUser)
//
//     console.log(courseNo)
//   }, 60000)
//
//
//   it('mock Data', async () => {
//     Config.setting.wechatConfig = {
//       id: '****************',
//       botUserId: 'QiaoQiao',
//       name: '麦子老师',
//       notifyGroupId: 'R:*****************',
//       classGroupId: 'R:*****************',
//       courseNo: 38
//     }
//
//
//     const event = {
//       'userId': 1018135,
//       'sku': '20250207003401',
//       'logid': 'gpeePWI6F8jrQireHqgI',
//       'vodId': 7310,
//       'studySchedule': '194',
//       'userPlaybackTime': 1688,
//       'duration': 870,
//       'event': 'course_study_guide'
//     }
//
//     const moerId = event.userId.toString()
//
//     const { trackId, courseNo } = await MoerEventForwardHandler.getCourseNo(moerId)
//   }, 60000)
//
//   it('123213', async () => {
//     console.log(await DataService.getCourseStartTime('****************_R*****************'))
//   }, 60000)
//
//   it('clearTasks', async () => {
//     await clearTasks('7881299815952228_****************')
//   }, 60000)
//
//   // it('123123', async () => {
//   //   const phoneNumber = RegexHelper.extractPhoneNumber('13911694830')
//   //   const moerUser = await MoerAPI.getUserByPhone(phoneNumber as string)
//   //   console.log(moerUser)
//   // }, 60000)
//
//   it('list', async () => {
//     console.log(JSON.stringify(await MoerAPI.getSimpleIdList(), null, 4))
//   }, 60000)
//
//   it('asda', async () => {
//     const [error, data] = await catchError(MoerAPI.getUserPhone({ externalUserId: 'wmXvL2CQAAxGqbp--Fks5z5umwuZ3KCP' }))
//     if (error) {
//       return ''
//     }
//
//     console.log(JSON.stringify(data.mobile, null, 4))
//
//   }, 60000)
//
//   it('chatHistory', async () => {
//     await PrismaMongoClient.getInstance().chat_history.deleteMany({
//       where: {
//         chat_id: '7881302192916883_1688854546332791'
//       },
//     })
//
//     // await LogStoreService.clearChatLog('7881301047907394_1688854546332791')
//
//     // console.log(await ChatHistoryService.formatHistory('7881301047907394_1688854546332791'))
//   }, 60000)
//
//   it('spaces', async () => {
//     function replaceMultipleBlankLines(input: string): string {
//       return input.replace(/(\r?\n\s*){2,}/g, '\n\n')
//     }
//   }, 60000)
//
//   it('chatId', async () => {
//     // 拉出当前期所有客户
//     // 向消息队列中添加一个任务
//     let chats = await PrismaMongoClient.getInstance().chat.findMany({
//       where: {
//         course_no: 37
//       },
//     })
//
//     chats = chats.filter((chat) => {
//       return !['哈哈哈', 'SYQ', '麦子', 'Horus', 'Tony Gu', 'god', '小仙女本仙', 'test'].includes(chat.contact.wx_name)
//     })
//
//     for (const chat of chats) {
//       const queue = new Queue(chat.id, {
//         connection: RedisDB.getInstance()
//       })
//
//       const task = {
//         chatId: chat.id,
//         userId: getUserId(chat.id),
//         name: TaskName.SalesPitch,
//         scheduleTime: {
//           is_course_week: true,
//           day: 3,
//           time: '21:50:00'
//         }
//       } as ITask
//
//       task.sendTime =  await calTaskTime(task.scheduleTime as IScheduleTime, task.chatId)
//
//       const job = ScheduleTask.taskToJob(task)
//
//       await queue.add(job.name, job.data, job.opts)
//     }
//
//
//     // chats = chats.filter((chat) => {
//     //   return ! ['哈哈哈', 'SYQ', '麦子', 'Horus', 'Tony Gu', 'god', '小仙女本仙', 'test'].includes(chat.contact.wx_name)
//     // })
//     //
//     // for (const chat of chats) {
//     //
//     // }
//
//   }, 60000)
//
//   it('123123asdasd', async () => {
//     await new CheckPreCourseCompletionTask().process({
//       name: TaskName.CheckPreCourseCompletion,
//       chatId: '1',
//       userId: '2',
//     })
//   }, 60000)
//
//   it('isInClass', async () => {
//     console.log(JSON.stringify(await DataService.isInClass('7881300027049437_****************', { day: 4 }), null, 4))
//   }, 60000)
//
//   it('isCompleteCourse', async () => {
//     console.log(await DataService.isCompletedCourse('7881301671907052_****************', { day: 1 }))
//   }, 60000)
//
//   it('isCompleteEnergyTest', async () => {
//     console.log(await DataService.getEnergyTestScore('7881300929922693_****************'))
//   }, 60000)
//
//   it('get User by phone', async () => {
//     console.log(JSON.stringify(await MoerAPI.getUserByPhone('18602912070'), null, 4))
//   }, 60000)
//
//   it('课程链接', async () => {
//     console.log(JSON.stringify(await DataService.getCourseLink(1, '7881300929922693_****************', true), null, 4))
//   }, 60000)
//
//   it('手动清空群任务', async () => {
//     const queue = new Queue('****************_R*****************', {
//       connection: RedisDB.getInstance()
//     })
//
//     await queue.obliterate({ force: true })
//   }, 60000)
//
//   it('更新手机号', async () => {
//     Config.setting.wechatConfig = {
//       id: '****************',
//       botUserId: 'MaiZi',
//       name: '麦子老师',
//       notifyGroupId: 'R:*****************',
//       classGroupId: 'R:*****************',
//       courseNo: 38
//     }
//
//     // 拉出当前期所有客户
//     // 向消息队列中添加一个任务
//     /**
//      * 注意 await 任务添加
//      */
//
//     // 拉当前账号，所有客户
//     let chats = await PrismaMongoClient.getInstance().chat.findMany({
//       where: {
//         // wx_id: '****************',
//         created_at: {
//           gte: new Date('2024-12-11')
//         }
//       }
//     })
//
//
//     const names = ['向日葵']
//     const mobile = {
//       '向日葵': '13632657606',
//     }
//
//     chats = chats.filter((chat) => {
//       return names.includes(chat.contact.wx_name)
//     })
//
//
//     async function updateInfo(chat) {
//       if (!names.includes(chat.contact.wx_name)) {
//         return
//       }
//
//       // @ts-ignore fk u
//       Config.setting.wechatConfig.id = chat.wx_id
//       const externalId = await JuziAPI.wxIdToExternalUserId(chat.contact.wx_id)
//       if (!externalId) {
//         return
//       }
//
//       // eslint-disable-next-line prefer-const
//       let [error, data] = await catchError(MoerAPI.getUserPhone({ externalUserId: externalId }))
//       if (mobile[chat.contact.wx_name]) {
//         data = {
//           mobile: mobile[chat.contact.wx_name],
//           contact_id: '',
//           name: '',
//           avatar: ''
//         }
//       }
//
//       if (error || !data || !data.mobile) {
//         return
//       }
//
//       if (data.mobile === '19119281868') {
//         return ''
//       }
//
//       const phone = data.mobile
//       const moerUser = await MoerAPI.getUserByPhone(phone)
//       const courseNo = DataService.parseCourseNo(moerUser)
//       await clearTasks(chat.id)
//
//       await ChatStatStoreManager.initState(chat.id) // 防止状态丢失
//
//       await NewCourseUser.create(chat.contact.wx_id, chat.id, courseNo, phone, moerUser.id.toString())
//
//       console.log(chat.contact.wx_name, phone)
//     }
//
//     const promises: Promise<any>[] = []
//     for (const chat of chats) {
//       promises.push(updateInfo(chat))
//       // await updateInfo(chat)
//     }
//
//     await Promise.all(promises)
//   }, 1E8)
//
//   it('test', async () => {
//     const chats = await PrismaMongoClient.getInstance().chat.findMany({
//       where:{
//         course_no: 38
//       }
//     })
//
//     const moerIds = chats.map((chat) => chat.moer_id)
//
//     console.log(moerIds)
//   }, 60000)
//
//   it('fixDate', async () => {
//     fixDate(DateHelper.add(new OriginalDate(), -1, 'day').toLocaleString())
//     // moerAPI
//     console.log(JSON.stringify(await MoerAPI.getUserById('876611'), null, 4))
//   }, 60000)
//
//   it('remove moerId', async () => {
//     await ChatDB.removeMoerId('7881300846030208_1688858254705213')
//   }, 60000)
//
//   it('transfer to moer', async () => {
//     const contact = await JuziAPI.getCustomerInfo('1688858254705213', '7881302298050442')
//
//     console.log(JSON.stringify(contact, null, 4))
//   }, 60000)
//
//   it('logger', async () => {
//     logger.trace({ chat_id: '7881300846030208_1688858254705213' }, 'hi')
//     logger.trace({ chat_id: '7881300846030208_1688858254705213' }, 'hello')
//
//     await sleep(5000)
//   }, 60000)
//
//   it('history', async () => {
//     await ChatHistoryService.addUserMessage('7881301047907394_1688858254705213', 'test')
//
//     const chatHistory = await ChatHistoryService.getLLMChatHistory('7881301047907394_1688858254705213', 3)
//
//     console.log(JSON.stringify(chatHistory, null, 4))
//   }, 60000)
//
//   it('ocr 修正', async () => {
//     const imageOcrResult =  await FreeSpiritOCR.recognizeUrl('https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/ocr_test/84343316-2c58-4789-bae5-efad29c7ac35.jpg.jpg')
//     const text = imageOcrResult.map((item) => item.text).join('')
//
//     function extractScore(text: string) {
//       try {
//         const pattern =  /(\d+)分\/\d+分/
//         const match = text.match(pattern)
//
//         if (!match) return null
//
//         let score = match[1]
//         if (score.length > 3) {
//           score = score.slice(-3)
//         }
//
//         const numberScore = Number(score)
//
//         // 多重验证
//         if (
//           !Number.isFinite(numberScore) || // 检查是否为有限数字
//             !Number.isInteger(numberScore) // 检查是否为整数
//         ) {
//           return null
//         }
//
//         return numberScore
//       } catch (error) {
//         return null
//       }
//     }
//
//     if (text.includes('能量频率层级测评')) {
//       // 如果能直接获取到分数，直接按照图片内的分数解读
//       const score = extractScore(text)
//       if (score) {
//         console.log(score)
//       }
//     }
//   }, 60000)
//
//   it('qqq', async () => {
//     const queue = new Queue('****************_R10915716002894380', {
//       connection: RedisDB.getInstance()
//     })
//
//     const completedJobs = await queue.getJobs(['completed'])
//     const failedJobs = await queue.getJobs(['failed'])
//     const activeJobs = await queue.getJobs(['active'])
//     const waitingJobs = await queue.getJobs(['waiting'])
//
//     console.log(JSON.stringify(completedJobs, null, 4))
//   }, 30000)
// })