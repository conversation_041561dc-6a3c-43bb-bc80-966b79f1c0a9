import mammoth from 'mammoth'
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import pdfParse from 'pdf-parse'
import { Buffer } from 'buffer'

const LIST_URL = 'https://aicowboy-app.integrity.com.cn/aiSupport/knowledgeList'
const DETAIL_URL = 'https://aicowboy-app.integrity.com.cn/aiSupport/knowledgeDetail'

export interface KnowledgeRecord {
  id: number
  unifiedId: number
  knowledgeId: string
  docId: string
  fileName: string
  fileExt: string | null
  fileUrl: string | null
  webUrl: string | null
  summary: string | null
  coverimgUrl: string | null
  md5: string | null
  fileSize: number | null
  statue: number
  statueText: string
  type: number
  mdResult: string | null
  webResult: string | null
  deleteit: boolean
  createTime: string   // 如果你要操作成 Date，可以写成 Date
  updateTime: string
  question: string
  answer: string
  sources: string | null
  fileText: string | null
}

export interface KnowledgeDetailData {
  records: KnowledgeRecord[]
  total: number
  size: number
  current: number
  orders: Array<{
    column: string
    asc: boolean
  }>
  optimizeCountSql: boolean
  searchCount: boolean
  maxLimit: number | null
  countId: string | null
  pages: number
}

export interface DetailResponse {
  code: number
  msg: string
  data: KnowledgeDetailData
}

type ListResponse = {
    code: number
    msg: string
    data: string[]
}

async function fetchDocxText(url: string): Promise<string> {
  try {
    console.log('fetching docx url:', url)
    const res = await fetch(url)
    if (!res.ok) {
      console.error(`fetch failed for ${url}, HTTP status: ${res.status}`)
      return '[DOCX获取失败]'
    }
    const arrayBuffer = await res.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    const result = await mammoth.extractRawText({ buffer })
    return result.value
  } catch (e) {
    console.error(`fetchDocxText error for url ${url}:`, e)
    return '[DOCX获取失败]'
  }
}

// 下载并解析 pdf
async function fetchPdfText(url: string): Promise<string> {
  try {
    console.log('fetching pdf url:', url)
    const res = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
        'Accept': 'application/pdf',
        'Referer': 'https://zhyt-file.9635.com.cn/'
      }
    })
    if (!res.ok) {
      console.error(`fetch failed for ${url}, HTTP status: ${res.status}`)
      return '[PDF获取失败]'
    }
    const buffer = Buffer.from(await res.arrayBuffer())
    const data = await pdfParse(buffer)
    return data.text
  } catch (e) {
    console.error(`fetchPdfText error for url ${url}:`, e)
    return '[PDF获取失败]'
  }
}

export async function fetchIdList(): Promise<string[]> {
  const res = await fetch(LIST_URL, { method: 'GET' })
  const json: ListResponse = (await res.json()) as ListResponse
  if (json.code !== 0) {
    throw new Error(`List request failed: ${json.msg}`)
  }
  console.log('idList:', json.data)
  return json.data
}

export async function fetchAllRecordsForId(knowledgeId: string): Promise<KnowledgeRecord[]> {
  const allRecords: KnowledgeRecord[] = []
  let pageNumber = 1
  const pageSize = 10

  while (true) {
    const params = new URLSearchParams({
      knowledgeId,
      pageNumber: String(pageNumber),
      pageSize: String(pageSize),
    })
    const url = `${DETAIL_URL}?${params.toString()}`
    const res = await fetch(url, { method: 'GET' })
    const json: DetailResponse = await res.json()
    if (json.code !== 0) {
      console.error(`Error fetching details for knowledgeId=${knowledgeId}, page=${pageNumber}: ${json.msg}`)
      break
    }

    const { records, pages } = json.data

    for (const rec of records) {
      const enriched = await enrichRecordWithFileText(rec)
      allRecords.push(enriched)
    }

    if (pageNumber >= pages) break
    pageNumber++
  }
  return allRecords
}

// 处理单个 record 文件
async function enrichRecordWithFileText(record: KnowledgeRecord): Promise<KnowledgeRecord> {
  if (
    record.fileUrl &&
        typeof record.fileUrl === 'string' &&
        (record.fileExt === 'docx' || record.fileExt === 'pdf')
  ) {
    try {
      let text = ''
      if (record.fileExt === 'docx') {
        text = await fetchDocxText(record.fileUrl)
      } else if (record.fileExt === 'pdf') {
        text = await fetchPdfText(record.fileUrl)
      }
      return { ...record, fileText: text }
    } catch (e) {
      return { ...record, fileText: '[文件解析失败]' }
    }
  } else {
    return record
  }
}