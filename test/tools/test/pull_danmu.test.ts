import { pull_danmu_by_course_no } from '../pull_danmu'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { DanmuAnalyzer } from '../../../bot/service/moer/components/danmu/danmu_analyzer'
import { ExtractUserSlotPrompt } from '../../../bot/service/moer/prompt/moer/userSlots'
import { ExtractUserSlots } from '../../../bot/service/moer/components/flow/helper/slotsExtract'
import { ChatStateStore, ChatStatStoreManager } from '../../../bot/service/moer/storage/chat_state_store'
import { ContextBuilder } from '../../../bot/service/moer/components/agent/context'
import { LLM } from '../../../bot/lib/ai/llm/LLM'
import { MemorySummaryPromptOnline } from '../../../bot/service/moer/prompt/moer/userMemorySummary'
import { MemoryStore } from '../../../bot/service/moer/components/memory/memory_store'
import { DataService } from '../../../bot/service/moer/getter/getData'
import { CsvHelper } from '../../../bot/lib/csv/csv_parse'
import path from 'path'
import { ObjectUtil } from '../../../bot/lib/object'
import { DanmuHelper } from '../../../bot/service/moer/components/danmu/danmu'
import { Queue } from 'bullmq'
import { RedisDB } from '../../../bot/model/redis/redis'
import { DanmuDB } from '../../../bot/service/moer/database/danmu'
import { DateHelper } from '../../../bot/lib/date/date'

describe('Test', function () {
  beforeAll(() => {

  })

  it('liveId', async () => {
    console.log(JSON.stringify(await DataService.getCourseInfoByCourseNo(47), null, 4))
  }, 60000)

  it('currentCourseNo', async () => {
    console.log(DataService.getCurrentWeekCourseNo())
  }, 60000)

  it('删除', async () => {
    await PrismaMongoClient.getInstance().danmu.deleteMany({
      where: {
        courseNo: 46
      }
    })
  }, 60000)

  it('拉取弹幕', async () => {
    await pull_danmu_by_course_no(47)
  }, 60000)

  it('处理单天的弹幕', async () => {
    // 注意如果是之前的时间，需要修改 courseStartTime, 跳转到正确的时间

    const chats = await  PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no: 46
      },
    })

    const qasMap = new Map<string, any[]>()
    for (const chat of chats) {
      if (!chat.moer_id)
        continue
      const qas = await  DanmuAnalyzer.analyzeDanmu(chat.moer_id, '5405070', 3)

      if (qas.length) {
        qasMap.set(chat.id, qas)
      }
    }

    // 定义一个对象来存储所有 chatId 对应的槽位信息
    const csvJson: any = {}

    let count = 0

    await Promise.all(Array.from(qasMap.entries()).map(async ([chatId, qas]) => {
      // 提取新增的槽位
      const extractPrompt = await ExtractUserSlotPrompt.format(JSON.stringify(qas))
      const currentUserSlots = await ExtractUserSlots.extract(extractPrompt, ExtractUserSlotPrompt.schema)
      console.log('增加的槽位', JSON.stringify(currentUserSlots, null, 4))

      // 初始化状态并获取原始槽位
      await ChatStatStoreManager.initState(chatId)
      const originalSlots = ContextBuilder.getUserSlotsText(chatId)
      console.log('原始槽位', originalSlots)

      // 合并槽位并获取更新后的槽位
      await ExtractUserSlots.extractUserSlots(JSON.stringify(qas), chatId)
      const prevUserSlots = ChatStateStore.get(chatId).userSlots

      let updatedSlotS = ''
      if (currentUserSlots) {
        const updatedSlots = await (ExtractUserSlots as any).mergeUserSlots(prevUserSlots, currentUserSlots)
        ChatStateStore.update(chatId, { userSlots: updatedSlots })
        updatedSlotS = ContextBuilder.getUserSlotsText(chatId)
        console.log('更新后的槽位',  updatedSlotS)
        // 保存
        // await DataService.saveChat(chatId, getUserId(chatId))
      }

      if (ObjectUtil.isEmptyObject(originalSlots)) {
        count++
        console.log('count:', count)
      }

      // 将收集到的数据存储到 csvJson 对象中
      csvJson[chatId] = {
        '增加的槽位': JSON.stringify(currentUserSlots, null, 4),
        '原始槽位': originalSlots,
        '更新后的槽位': updatedSlotS
      }
    }))

    // 调用 write2DJson 将数据写入 CSV
    CsvHelper.write2DJson(path.join(__dirname, 'output.csv'), csvJson, ['增加的槽位', '原始槽位', '更新后的槽位'])
  }, 1E8)

  it('拉取第四天弹幕', async () => {
    // console.log(JSON.stringify(await DataService.getCourseInfoByCourseNo(46), null, 4))
    // const rawDanmus = await DanmuDB.getDanmusByMoerIdAndLiveId('977300', '5405070')
    const rawDanmus = await DanmuDB.getDanmusByMoerIdAndLiveId('977300', '5405074')

    for (const danmu of rawDanmus) {
      const danmuTime = new Date(danmu.time)
      const minuteDifference = DateHelper.diff(new Date('2024-12-19 20:00:00'), danmuTime, 'minute')

      console.log(JSON.stringify({
        time: minuteDifference,
        originTime: new Date(danmu.time).toLocaleString(),
        content: danmu.content
      }, null, 4))
    }

    // console.log(JSON.stringify(rawDanmus, null, 4))
  }, 60000)

  it('拉取单人弹幕', async () => {
    const qas = await DanmuAnalyzer.analyzeDanmu('977300', '5405060', 1)

    console.log(JSON.stringify(qas, null, 4))

    // 提取槽位
    const extractPrompt = await ExtractUserSlotPrompt.format(JSON.stringify(qas))

    const currentUserSlots = await ExtractUserSlots.extract(extractPrompt, ExtractUserSlotPrompt.schema)

    console.log('增加的槽位', JSON.stringify(currentUserSlots, null, 4))

    await ChatStatStoreManager.initState('7881299785944651_1688856322643146')
    console.log('原始槽位', ContextBuilder.getUserSlotsText('7881299785944651_1688856322643146'))

    // 合并槽位
    await ExtractUserSlots.extractUserSlots(JSON.stringify(qas), '7881299785944651_1688856322643146')
    console.log('更新后的槽位', ContextBuilder.getUserSlotsText('7881299785944651_1688856322643146'))
  }, 1E8)

  it('整合进入记忆', async () => {
    const llm = new LLM({ model: 'gpt-4.1-mini' })

    const formattedSummaryPrompt = await MemorySummaryPromptOnline.format('你好')
    const summarizedMemory = await llm.predict(formattedSummaryPrompt)

    console.log(summarizedMemory)
  }, 60000)

  it('omg', async () => {
    // await ChatHistoryService.clearChatHistory('7881300846030208_1688857003605938', false)
    await MemoryStore.pushRecentMemoryToVectorDB('7881300846030208_1688857003605938', '')
  }, 60000)


  it('冷启动任务', async () => {
    // pullDanmu, 清空
    const queue = new Queue('pullDanmu', {
      connection: RedisDB.getInstance()
    })

    await queue.obliterate({ force: true })


    await DanmuHelper.startPullDanmuTask()
  }, 60000)


  it('danmuServer', async () => {
    const queue = new Queue('pullDanmu', {
      connection: RedisDB.getInstance()
    })

    console.log(JSON.stringify(await queue.getJobs(), null, 4))
  }, 60000)


})