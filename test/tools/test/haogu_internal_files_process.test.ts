// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { fetchIdList, fetchAllRecordsForId, KnowledgeRecord } from '../haogu_internal_files_process.ts'
import fs from 'fs'
import path from 'path'


describe('KnowledgeRecords', () => {
  it('should fetch all records for all knowledgeIds', async () => {
    // 获取 id 列表
    const idList = await fetchIdList()
    expect(Array.isArray(idList)).toBe(true)
    expect(idList.length).toBeGreaterThan(0)

    let allRecords: KnowledgeRecord[] = []
    for (const knowledgeId of idList) {
      console.log(`Fetching records for knowledgeId: ${knowledgeId}`)
      const records = await fetchAllRecordsForId(knowledgeId)
      console.log(`Fetched ${records.length} records for knowledgeId: ${knowledgeId}`)
      allRecords = allRecords.concat(records)
    }

    // 打印带fileText的record
    // 带fileText属于有docx或者pdf格式解析的，解析内存存放在fileText里，其他的以问答对的形式放在question-answer字段里
    const fileTextCount = allRecords.filter((r) => r.fileText && typeof r.fileText === 'string' && r.fileText !== '').length
    console.log('records with fileText:', fileTextCount)
    allRecords
      .filter((r) => r.fileText)
      .forEach((r) => {
        console.log('fileExt:', r.fileExt, 'fileUrl:', r.fileUrl)
        console.log('fileText-preview:', (r.fileText || '').slice(0, 200))
      })
    console.log(`Total records fetched: ${allRecords.length}`)
    // 这里可以加断言
    expect(allRecords.length).toBeGreaterThan(0)
    //saveAsJson(allRecords)
  }, 8E7) // 超时时间设置为 80 秒，避免超时
})

const saveAsJson = (data: any[]) => {
  const filePath = path.join(__dirname, 'allRecords.json')
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8')
  console.log(`JSON 文件已保存: ${filePath}`)
}
