import { PromptTemplate } from '@langchain/core/prompts'
import { LLM } from '../../bot/lib/ai/llm/LLM'
import * as mammoth from 'mammoth'
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter'
import { FileHelper } from '../../bot/lib/file'
import * as fs from 'fs'


export class QAExtraction {


  public static async extract(filepath: string, resPath: string, docName: string) {
    const resList: any[] = []

    const chunks = await this.segmentDoc(filepath)
    for (const chunk of chunks) {
      const res = JSON.parse(await this.getLLMRes(chunk))
      console.log('chunk', chunk)
      console.log('res', res)
      const questions = res.qas.map((qa) => qa.question)
      const answers = res.qas.map((qa) => qa.answer)
      resList.push(this.structureOutput(questions, answers, chunk, docName))
    }

    await FileHelper.writeFile(resPath, JSON.stringify(resList))
  }

  private static structureOutput(questions: string[] | null, answers: string[] | null, chunk: string, docName: string) {
    if (questions === null || answers === null || questions.length != answers.length) {
      console.warn('Invalid question/answer pair:', questions, answers)
      return {
        chunk: chunk,
        qas: [{
          'q': '',
          'a': ''
        }],
        doc: docName,
      }
    }

    return {
      chunk: chunk,
      qas: questions.map((question, index) => ({
        'q': question,
        'a': answers[index]
      })),
      doc: docName,
    }


  }

  private static async segmentDoc(filepath: string) {
    if (!fs.existsSync(filepath)) {
      throw new Error(`File not found: ${filepath}`)
    }

    const extension = filepath.split('.').pop()?.toLowerCase()
    let content = ''

    try {
      if (extension === 'docx') {
        const dataBuffer = fs.readFileSync(filepath)
        const result = await mammoth.extractRawText({ buffer: dataBuffer })
        content = result.value
      } else if (extension === 'doc') {
        const dataBuffer = fs.readFileSync(filepath)
        const result = await mammoth.extractRawText({ buffer: dataBuffer })
        content = result.value
      } else {
        const fileStream = fs.readFileSync(filepath, 'utf-8')
        content = fileStream.toString()
      }
    } catch (fileError) {
      throw new Error(`Failed to read file: ${fileError}`)
    }

    try {
      const chunks = await this.recursiveTextSplitter(content, 500, 250)
      return chunks.filter((chunk) => chunk.trim().length > 0)
    } catch (apiError) {
      throw new Error(`Failed to split document ${apiError}`)
    }



  }

  private static recursiveTextSplitter(text: string, max_length: number, overlap: number): Promise<string[]> {
    const splitter = new RecursiveCharacterTextSplitter({
      chunkSize: max_length,
      chunkOverlap: overlap,
      keepSeparator: true
    })
    return splitter.splitText(text)
  }


  private static async getLLMRes(document: string) {
    const promptTemplate = PromptTemplate.fromTemplate(`# 背景
本节课由刘瑞老师主讲，首次公开筹码峰无延迟用法，系统讲解双线和一、四点共振、锁仓破仓三大核心方法，并通过嵘泰股份案例演示完整买卖逻辑，强调参数统一性与主力意图识别，为后续六天训练营与半年实战班奠定理论基础。

# 任务
请分析给定的文档块，并生成相关的中文问答对，给到的文档块的内容与股票交易培训课程相关。你需要根据文档内容你与学员的关系（你是课程的助教老师，学员是股票投资者（散户）），从文档中提取可能的问答对。

# 文档块内容
{document}

# 要求：
1. 要求问题简洁易懂
2. 如果无法生成有意义的问答对，请跳过这部分内容。
3. 确保答案包含有效信息，避免在问题或答案中提及 "相关文件"，"相关文本"，"相关渠道"或者"文档"等词汇。
4. 确保答案详细、完整，并准确反映源内容的中文描述（如适用，请使用文档中的原始内容）。

# 用JSON格式输出
{{
    qas:[
      {{
        "question":"",
        "answer":""
      }}
    ]
}}

`)


    return await LLM.predict(
      promptTemplate,
      { model: 'gpt-4.1', responseJSON: true },
      { document }
    )
  }
}

