import { IMoerEvent } from '../client/client_server'
import logger from '../../bot/model/logger/logger'
import { ObjectUtil } from '../../bot/lib/object'
import { DataService } from '../../bot/service/moer/getter/getData'
import { ClientAccountConfig } from '../config/account'
import { Retry } from '../../bot/lib/retry/retry'
import axios from 'axios'
import { StringHelper } from '../../bot/lib/string'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
import { RedisCacheDB } from '../../bot/model/redis/redis_cache'
import { EventTracker, IEventType } from '../../bot/model/logger/data_driven'
import { MoerAPI } from '../../bot/model/moer_api/moer'
import { DateHelper } from '../../bot/lib/date/date'
import { IXiaoHongshuOrderEvent } from './moer_event'
import { ScheduleTask } from '../../bot/service/moer/components/schedule/schedule'
import { TaskName } from '../../bot/service/moer/components/flow/schedule/type'


interface LiveStreamLog {
    channelId: string          // 频道号
    groupId?: string           // 分组id，非必传
    viewerId: string           // 参会人ID
    nickName?: string          // 客户昵称
    logType: number            // 日志类型
    interactType: string       // 日志类型对应枚举
    logTime: number            // 日志时间戳
    ipAddress?: string         // IP地址，非必传
    userAgent?: string         // UA信息，非必传
    referer?: string           // referer信息 - 请求来源，非必传
    viewerCount: number        // 当前参会人数
    timestamp: number          // 13位毫秒级时间戳
    sessionId: string | null   // 当前场次
    userOrigin?: string        // 客户来源，非必传
    content?: string           // logType为101和102时，表示学员名单的oss地址
    role: 'teacher' | 'guest' | 'viewer' | 'assistant' | 'attendee' | 'listener' // 客户身份信息
    inClass?: number           // 频道是否在直播中，1表示正在直播，2表示不在直播
    status: number             // 状态码（可能表示成功或失败的标志）
    websocketId: string        // websocket连接id，用于计算参与直播时长
}

interface ILiveStreamEvent {
    data: LiveStreamLog[]      // 日志数据列表
    logid: string              // 日志ID
    event: string              // 事件类型
}

export interface EnergyTestEvent {
    logid: string
    examScore: number
    userId: number | string
    mobile: string
    event: string

    project?: 'mainland' | 'international' // 国内/国际
}

export interface PaymentEvent {
    sku: string
    userId: number | string
    event: string

    project?: 'mainland' | 'international' // 国内/国际
}

interface IBindWechatUserEvent {
    logid: string
    event: string
    externalUserID: string
    name: string
    unionid: string
    avatar: string
    createdAt: string
    userId: number
    mobile: string

    project?: 'mainland' | 'international' // 国内/国际
}


interface IRumengyingOrderEvent {
    logid: string // 日志 ID
    event: string // 事件类型
    userId: number // 客户 ID
    nationcode?: string // 国家代码
    mobile: string // 手机号
    goodsName: string // 商品名称
    sku: string // 商品 SKU
    stage: number // 阶段
    simpleName: string // 老师名称
    simpleId: number // 老师 ID
    transferWay: string // 来源渠道
    form_nationcode?: string // 海外邀请链接表单中填写的国家代码
    form_mobile?: string // 表单中填写的手机号
    whats_app?: string // WhatsApp 号
    email?: string // 邮箱
    wechat?: string // 微信号
    line?: string // line账号
    facebook?: string // Facebook 号
    transfer_no?: string

    project?: 'mainland' | 'international' // 国内/国际
}

interface IMergeUserEvent {
  event: string
  oldUserId: number
  oldMobile: string
  newUserId: number
  newMobile: string
  project?: 'mainland' | 'international' // 国内/国际
}

interface ITanglangLiveEnterExitEvent {
  logid: string              // 日志ID
  event: string              // 事件类型，固定值：tanglang_live_enter_exit
  pushType: string           // 推送事件类型，固定值：LIVE_IN_OUT
  messageId: string          // 推送消息id
  actionType: 'ENTER_LIVE' | 'LEAVE_LIVE'  // 进出直播间类型
  time: string               // 发生时间 yyyy-MM-dd HH:mm:ss
  liveNum: number            // 直播间id
  scrmUserCode: number       // 客户id
  unionId: string            // unionId (就是 userId 就是 moerId)
  sessionId: string          // 学员进入直播间的sessionId,单次进入和退出直播间的值相同
  companyId: number          // 公司id
  shareUserId: string | null // 分享人id
  openId: string | null      // openId
  externalUserList: Array<{
    corpId: string           // 企微id
    externalUserId: string   // 外部联系人id
  }>

  project?: 'mainland' | 'international' // 国内/国际
}



/**
 * 只做转发，不做事件处理
 */
export class MoerEventForwardHandler {
  private static moerIdMap: Map<string, string> | undefined

  private static async forwardEventByMoerId(event: IMoerEvent, moerId: string) {
    try {
      const wx_id = await DataService.getWxIdByMoerId(moerId)
      if (!wx_id) {
        return
      }

      const serverAddress = await this.getServerAddress(wx_id)

      if (serverAddress) {
        await this.dispatchEventToServer(serverAddress, event)
      }
    } catch (e) {
      console.error(`Forward event to server error: ${e}`)
    }
  }

  private static async forwardEventByAccountId(event: IMoerEvent, accountId: string) {
    try {
      const serverAddress = await this.getServerAddress(accountId)

      if (serverAddress) {
        await this.dispatchEventToServer(serverAddress, event)
      }
    } catch (e) {
      console.error(`Forward event to server error: ${e}`)
    }
  }

  private static async getServerAddress(wechatId: string) {
    const serverAddress = await ClientAccountConfig.getServerAddressByWechatId(wechatId)

    if (!serverAddress) {
      return null
    }

    return serverAddress
  }


  public static async handle(event: IMoerEvent) {
    if (ObjectUtil.isEmptyObject(event)) {
      // 忽略空事件
      return
    }

    logger.log(JSON.stringify(event, null, 4))

    try {
      switch (event.event) {
        case 'course_study_guide':
          this.handlePreCourseComplete(event)
          break

        case 'jinshuju_user_exam_score':
          this.handleCompleteEnergyTest(event as EnergyTestEvent)
          break

        case 'course_study_review':
          this.handleCourseComplete(event)
          break

        case 'course_pay_paid':
          this.handlePaidCourse(event as PaymentEvent)
          break

        case 'course_pay_unpaid':
          this.handlePaymentFailure(event as PaymentEvent)
          break

        case 'live_stream_status':
          this.handleLiveStreamStatus(event as ILiveStreamEvent)
          break

        case 'tanglang_live_enter_exit':
          this.handleTanglangLiveEnterExit(event as ITanglangLiveEnterExitEvent)
          break

        case 'add_enterprise_wechat_user':
          this.handleAddEnterpriseWechatUser(event as IBindWechatUserEvent)
          break

        case 'xiaohongshu_order':
        case 'baoding_order':
          this.handleXiaoHongshuOrder(event as IXiaoHongshuOrderEvent)
          break

        case 'rumengying_order':
          this.handleRumenyingOrder(event as IRumengyingOrderEvent)
          break

        case 'merge_user':
          this.handleMergeUser(event as IMergeUserEvent)
          break

        default:
          this.handleUnknownEvent(event)
          break
      }
    } catch (e) {
      logger.error('moer event handler error:', e)
    }
  }

  static async handlePreCourseComplete(event: IMoerEvent) {
    const moerId = event.userId.toString()
    await this.forwardEventByMoerId(event, moerId)
    try {
      const { trackId, courseNo } = await this.getCourseNo(moerId)
      EventTracker.track(trackId, IEventType.PreCourseComplete, {
        more_id: moerId,
        course_no: courseNo,
        class_day: 0,
      })
    } catch (error) {
      logger.error('Error in log of handlePreCourseComplete:', error)
    }
  }

  public static async handleCompleteEnergyTest(event: EnergyTestEvent) {
    const moerId = event.userId.toString()
    await this.forwardEventByMoerId(event, moerId)
    try {
      const { trackId, courseNo } = await this.getCourseNo(moerId)
      EventTracker.track(trackId, IEventType.EnergyTestScore, {
        more_id: moerId,
        course_no: courseNo,
        test_score: event?.examScore
      })
    } catch (error) {
      logger.error('Error in log of handleCompleteEnergyTest:', error)
    }
  }

  public static async handleCourseComplete(event: IMoerEvent) {
    const moerId = event.userId.toString()
    await this.forwardEventByMoerId(event, moerId)

    try {
      const vodId = event.vodId
      const { trackId, courseNo, classDay, liveId } = await this.getCourseNo(moerId, vodId)
      EventTracker.track(trackId, IEventType.CourseComplete, {
        more_id: moerId,
        course_no: courseNo,
        class_day: classDay,
        live_id: liveId,
      })
    } catch (error) {
      logger.error('Error in log of handleCourseComplete:', error)
    }
  }

  public static async handlePaidCourse(event: PaymentEvent) {
    const moerId = event.userId.toString()
    await this.forwardEventByMoerId(event, moerId)
    try {
      const chatId = await DataService.getChatIdByMoerId(moerId)
      const trackId: string = chatId ? chatId : moerId
      const sku = event.sku
      EventTracker.track(trackId, IEventType.PaymentComplete, {
        more_id: moerId,
        sku: sku,
      })
    } catch (error) {
      logger.error('Error in log of handlePaidCourse:', error)
    }
  }

  public static async handlePaymentFailure(event: IMoerEvent) {
    const moerId = event.userId.toString()
    await this.forwardEventByMoerId(event, moerId)
    try {
      const chatId = await DataService.getChatIdByMoerId(moerId)
      const trackId: string = chatId ? chatId : moerId
      const sku = event.sku
      EventTracker.track(trackId, IEventType.PaymentNotCompleted, {
        more_id: moerId,
        sku: sku,
      })
    } catch (error) {
      logger.error('Error in track of handlePaymentFailure:', error)
    }
  }

  public static async handleLiveStreamStatus(event: ILiveStreamEvent) {
    const moerIdMap = await this.getCurrentMoerIdMap()

    for (const liveStreamData of event.data) {
      const moerId = liveStreamData.viewerId
      if (moerId && StringHelper.isNumber(moerId) && moerIdMap.get(moerId)) {
        await this.forwardEventByMoerId({
          event: event.event,
          ...liveStreamData
        }, moerId)
      }
    }

    try {
      const class_day = DateHelper.getWeekDay()
      for (const liveStreamData of event.data) {
        const moerId = liveStreamData.viewerId

        EventTracker.track(moerId, IEventType.liveStatusChange, {
          more_id: moerId,
          course_no: DataService.getCurrentWeekCourseNo(),
          class_day: class_day,
          live_id: liveStreamData?.channelId,
          status: liveStreamData?.interactType,
          log_time: liveStreamData?.logTime,
          websocket_id: liveStreamData?.websocketId
        })
      }
    } catch (error) {
      logger.error('Error in track of liveStreamStatus:', error)
    }
  }

  public static async handleTanglangLiveEnterExit(event: ITanglangLiveEnterExitEvent) {
    const moerId = event.unionId // unionId 就是 userId 就是 moerId
    const moerIdMap = await this.getCurrentMoerIdMap()

    // 转发事件到对应的服务器
    if (moerId && StringHelper.isNumber(moerId) && moerIdMap.get(moerId)) {
      await this.forwardEventByMoerId(event, moerId)
    }

    try {
      const class_day = DateHelper.getWeekDay()

      // 事件追踪，类似于 live_stream_status 的处理方式
      EventTracker.track(moerId, IEventType.liveStatusChange, {
        more_id: moerId,
        course_no: DataService.getCurrentWeekCourseNo(),
        class_day: class_day,
        live_id: event.liveNum.toString(),
        status: event.actionType,
        log_time: new Date(event.time).getTime(),
        session_id: event.sessionId,
        scrm_user_code: event.scrmUserCode,
        company_id: event.companyId
      })
    } catch (error) {
      logger.error('Error in track of tanglangLiveEnterExit:', error)
    }
  }

  private static async getCurrentMoerIdMap() {
    if (this.moerIdMap && await new RedisCacheDB('moerIdMap').get()) {
      return this.moerIdMap
    }

    // 取出所有客户 moerId 并缓存一下
    const chatsWithMoerId = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        moer_id: {
          isSet: true
        },
        course_no: {
          in: [
            DataService.getCurrentWeekCourseNo(), // A 线期数
            DataService.getCurrentWeekCourseNo() - 67 // B 线期数
          ]
        }
      },
      select: {
        moer_id: true,
        id: true
      }
    })

    this.moerIdMap = new Map<string, string>()
    chatsWithMoerId.forEach((chat) => {
      if (chat) {
        (this.moerIdMap as Map<string, string>).set(chat.moer_id as string,  chat.id)
      }
    })

    await new RedisCacheDB('moerIdMap').set(this.moerIdMap,  3 * 60 * 60) // 缓存 3 小时， 新增的 moerId 一般不是当天看直播的人群

    return this.moerIdMap
  }

  public static async handleUnknownEvent(event: IMoerEvent) {
    const moerId = event.userId ? event.userId.toString() : ''
    if (moerId) {
      await this.forwardEventByMoerId(event, moerId)
    } else {
      logger.debug('unknown moer event without userId:', JSON.stringify(event))
    }
  }

  public static async getCourseNo(moerId: string, vodId?: number) {
    const chatId = await DataService.getChatIdByMoerId(moerId)
    let courseNo: number | null | undefined
    let classDay: number | undefined
    let liveId: number | undefined

    if (chatId) {
      courseNo = await DataService.getCourseNoByChatId(chatId)
    } else {
      const user = await MoerAPI.getUserById(moerId)
      courseNo = user?.data?.userGoodsSeries?.[0]?.stage ?? null
    }

    const trackId: string = chatId ? chatId : moerId

    if (vodId && typeof courseNo === 'number') {
      const courseInfo = await DataService.getCourseInfoByCourseNo(courseNo)
      classDay = courseInfo?.resource?.find((item) => item.vodId === vodId)?.day
      liveId = courseInfo?.resource?.find((item) => item.vodId === vodId)?.liveId
      return { trackId, courseNo, classDay, liveId }
    }
    return { trackId, courseNo }
  }

  private static async handleAddEnterpriseWechatUser(event: IBindWechatUserEvent) {
    if (!event.userId) {
      // 建立 moer externalId 的映射
      const redis = new RedisCacheDB(event.externalUserID)
      await redis.set(event, 24 * 60 * 60)
      return
    }

    const userInfo = await MoerAPI.getUserById(event.userId.toString(10))

    const courseNo = userInfo.data.userGoodsSeries.find((item) => item.type === 1)?.stage
    if (!courseNo) {
      logger.debug('cannot get course_no when bind wechat', JSON.stringify(userInfo))
    }
    const updatedEvent = { ...event, course_no: courseNo }
    EventTracker.track(event.userId.toString(10), IEventType.BindWechat, updatedEvent)
    // 建立 moer externalId 的映射
    const redis = new RedisCacheDB(event.externalUserID)
    await redis.set(event, 24 * 60 * 60)
  }

  private static async dispatchEventToServer(serverAddress: string, event: any) {
    if (!serverAddress) {
      console.error('没有找到对应的服务器地址')
      return
    }

    try {
      await Retry.retry(4, async () => {
        await axios.post(`${serverAddress}/moer/event`, event, { insecureHTTPParser: true })
      }, {
        delayFunc :(retryCount) => {
          if (retryCount === 1) return 2 * 60 * 1000  // 2分钟
          if (retryCount === 2) return 10 * 60 * 1000 // 10分钟
          if (retryCount === 3) return 30 * 60 * 1000 // 30分钟
          return 0  // 之后不再进行重试
        }
      })
    } catch (e) {
      logger.error('事件分发失败：', serverAddress, e)
    }
  }

  private static async handleXiaoHongshuOrder(event: IXiaoHongshuOrderEvent) {
    const chatId = event.wxId
    const splitIds =  chatId.split('_')
    if (splitIds.length !== 2) {
      return
    }

    const accountId = splitIds[1]

    await this.forwardEventByAccountId(event, accountId)
  }

  private static async handleRumenyingOrder(event: IRumengyingOrderEvent) {
    const simpleId = event.simpleId.toString()

    // 全量线索埋点
    EventTracker.track(event.userId.toString(10), IEventType.BootcampOrder, event)

    const AISimpleIdMap: Record<string, string> = {
      '695': '****************', // 彤彤
      '669': '****************', // 乔乔
      '775': '****************', // 墨尔3
      '791': '****************', // 墨尔4
      '769': '****************', // 墨尔5
      '753': '****************', // 冥想6
      '754': '****************', // 冥想7
      '837': '****************', // 麦子 9
      '838': '****************', // 麦子 10
      '840': '****************', // 麦子 11
      '836': '****************', // 麦子 12
      '764': '****************', // 麦子 13
      '844': '****************', // 麦子 15
      '924': '****************', // 麦子 18
      '923': '****************', // 麦子 19
      '946': '***********14261'  // 麦子 20
    }


    // 筛选出我们这边 AI 接量的 账号
    const wechatId = AISimpleIdMap[simpleId]
    if (!wechatId) {
      logger.warn(`No wechatId found for simpleId: ${simpleId}`)
      return
    }

    if (event.project === 'international') {
      await ScheduleTask.addTask(wechatId, TaskName.RumenyingOrder,  DateHelper.add(new Date(), 2, 'second'), { ...event, retryTime: 1 })
    } else {
      // 5min 自动加微流程
      await ScheduleTask.addTask(wechatId, TaskName.RumenyingOrder,  DateHelper.add(new Date(), 5, 'minute'), { ...event, retryTime: 1 })
      //电话加微 10min
      await ScheduleTask.addTask(wechatId, TaskName.RumenyingOrder,  DateHelper.add(new Date(), 10, 'minute'), { ...event, retryTime: 2 })
      //电话加微 30min
      await ScheduleTask.addTask(wechatId, TaskName.RumenyingOrder,  DateHelper.add(new Date(), 30, 'minute'), { ...event, retryTime: 30 })
      //通知人工加微40min
      await ScheduleTask.addTask(wechatId, TaskName.RumenyingOrder,  DateHelper.add(new Date(), 40, 'minute'), { ...event, retryTime: 3 })
      //一天后电话加微
      await ScheduleTask.addTask(wechatId, TaskName.RumenyingOrder,  DateHelper.add(new Date(), 1, 'day'), { ...event, retryTime: 4 })

      //周日统一电话加微
      const sundayDay = DateHelper.add(new Date(), (7 - new Date().getDay()) % 7, 'day')
      const sunday5PM = new Date(sundayDay.setHours(17, 0, 0, 0))
      await ScheduleTask.addTask(wechatId, TaskName.RumenyingOrder,  sunday5PM, { ...event, retryTime: 5 })
    }
  }

  private static async handleMergeUser(event: IMergeUserEvent) {
    const moerId = event.oldUserId
    await this.forwardEventByMoerId(event, moerId.toString(10))
  }

  static forwardByRegion(data: IMoerEvent) {
    const CHINA_MOER_EVENT_SERVER_ADDRESS = 'http://*************:4002'
    const INTERNATIONAL_MOER_EVENT_SERVER_ADDRESS = 'http://***************:4003'

    if (data.project && typeof data.project === 'string') {
      if (data.project === 'international') {
        this.dispatchEventToServer(INTERNATIONAL_MOER_EVENT_SERVER_ADDRESS, data)
      } else {
        this.dispatchEventToServer(CHINA_MOER_EVENT_SERVER_ADDRESS, data)
      }
      return
    }

    // 没填 project 的话，两个服务都发送
    this.dispatchEventToServer(INTERNATIONAL_MOER_EVENT_SERVER_ADDRESS, data)
    this.dispatchEventToServer(CHINA_MOER_EVENT_SERVER_ADDRESS, data)
  }
}