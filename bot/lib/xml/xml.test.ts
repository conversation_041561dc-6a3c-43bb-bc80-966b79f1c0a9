import { XMLHelper } from './xml'

describe('Test', function () {
  beforeAll(() => {

  })

  it('parse', async () => {
    const xml = { 'contentXml':'<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>深度学习入门效率翻倍！给大家找到了一个涵盖小土堆pytorch、吴恩达深度学习、李沐动手学深度学习课程的完整版听课笔记！</title>\n\t\t<des>哔哩哔哩</des>\n\t\t<action />\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wx7564fd5313d24844&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<cdnthumburl>3057020100044b3049020100020427f7552d02032df0f20204c257bc77020465e83ba1042462646437323733632d396663652d346165322d613532392d6134616436366565663130650204051408030201000405004c4f8e00</cdnthumburl>\n\t\t\t<cdnthumbmd5>223fc1c2324c17b9b8547a7bb4c2bcd1</cdnthumbmd5>\n\t\t\t<cdnthumblength>92470</cdnthumblength>\n\t\t\t<cdnthumbwidth>500</cdnthumbwidth>\n\t\t\t<cdnthumbheight>400</cdnthumbheight>\n\t\t\t<cdnthumbaeskey>7026141f5cb7bbf1c80e6992a832b58e</cdnthumbaeskey>\n\t\t\t<aeskey>7026141f5cb7bbf1c80e6992a832b58e</aeskey>\n\t\t\t<encryver>0</encryver>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername>gh_cd19667c4224@app</sourceusername>\n\t\t<sourcedisplayname>哔哩哔哩</sourcedisplayname>\n\t\t<thumburl />\n\t\t<md5>223fc1c2324c17b9b8547a7bb4c2bcd1</md5>\n\t\t<statextstr />\n\t\t<weappinfo>\n\t\t\t<username><![CDATA[gh_cd19667c4224@app]]></username>\n\t\t\t<appid><![CDATA[wx7564fd5313d24844]]></appid>\n\t\t\t<type>2</type>\n\t\t\t<version>249</version>\n\t\t\t<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/9gYq0FHZpd0YyuQ2ZWFlMiat21cmISmjSnWJiaz3xS5HzoeXRGQndzm1VgwmiaZHuE1ibaBTzUGMjiaYWpYWU3bBtRw/640?wx_fmt=png&wxfrom=200]]></weappiconurl>\n\t\t\t<pagepath><![CDATA[pages/video/video.html?avid=1001496848&page=0&share_times=2]]></pagepath>\n\t\t\t<shareId><![CDATA[0_wx7564fd5313d24844_386b6bafe603da58e56922d9dabd1179_1709718432_0]]></shareId>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t</appmsg>\n\t<fromusername>wxid_8003740064812</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'previewImage':'https://ac-java-user.oss-cn-shanghai.aliyuncs.com/weremote/chat-logs/091A9740B8254CF8832ACD9F5F717071/028e0c6052acf7f91367a4ac7042747f/3/54fcc089d6ea12169ab4d87c4e4e89015131d5fd', 'type':'miniprogram' }
    const obj = XMLHelper.parse(xml.contentXml)

    console.log(obj?.msg?.appmsg?.title)
  })

  it('extract', async () => {
    const xml = ` <Conversion Process>
    - Analyze the user query: The user mentions they have an associate degree (大专), want to easily obtain a diploma (水一个学历), and have a budget of around 50,000 RMB per year (预算每年5万左右).
    - Map to ElasticSearch fields:
      - "minimumEducationRequirement" should be "专科" since the user has an associate degree.
      - "requirementType" should be "拿文凭" as the user wants to obtain a diploma.
      - "budgetLowerBound" and "budgetUpperBound" should both be set to 5 (since the budget is in tens of thousands, and the ElasticSearch field expects values in millions).
    - Construct the JSON query accordingly.
    
   <Pre-query JSON>
    {
      "minimumEducationRequirement": "专科",
      "requirementType": "拿文凭",
      "budgetLowerBound": 5,
      "budgetUpperBound": 5
    }
    </Pre-query JSON>
    }`


    const content = XMLHelper.extractContent(xml, 'Pre-query JSON')
    console.log(content)
  }, 30000)
  it('extract userslots', async () => {
    const xml = `<think>
从对话中可以提取以下信息：
1. 客户的能量测评分数是161，属于负能量范围。
2. 客户目前处于低能量状态，感到疲倦、缺乏动力、烦躁和紧张。
3. 客户身体有躯体化症状，并因此感到焦虑。
4. 客户对自己的状况有了察觉，并试图寻找解决方式。
5. 客户计划参加冥想课程以改善状态。

这些信息可以归类为痛点（情绪焦虑、身体躯体化症状）、冥想目标（缓解压力）、以及客户的自我觉察和行动力。没有明确提及客户的年龄、性别或居住地等基本信息，也未涉及兴趣爱好或工作相关内容。

</think>
<result>
- 痛点::情绪焦虑::客户感到疲倦、缺乏动力、烦躁和紧张，同时因身体躯体化症状而感到焦虑
- 痛点::身体躯体化症状::客户提到自己有胃疼、心悸和肌肉紧张等问题
- 冥想目标::缓解压力::客户希望通过冥想改善自身状态
- 自我觉察与行动力::现状察觉与解决尝试::客户对自己的状况有了察觉，并试图寻找解决方式
</result>`


    const content = XMLHelper.extractContent(xml, 'result')
    console.log(content)
  }, 30000)


})