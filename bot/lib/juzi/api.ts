import axios from 'axios'
import { Config } from '../../config/config'
import { CreateGroupParams, IWecomMessage } from './type'
import { UUID } from '../uuid/uuid'
import { JuZiWecomClient } from './client'
import * as fs from 'fs'
import * as path from 'path'
import logger from '../../model/logger/logger'
import { randomSleep, sleep } from '../schedule/schedule'

interface ICommonResponse {
  errcode: number
  errmsg: string
  data: any
}

interface IChangeRoomNameParam {
  imRoomId: string     // The ID of the room to be renamed
  imBotId: string      // The ID of the bot responsible for the room
  name: string         // The new name for the room
}

interface IWxIdToExternalIdResponse {
  errcode: number
  errmsg: string
  externalUserId: string
}

interface IGroupInfoData {
  errcode: number // 错误码
  errmsg: string // 错误原因
  data: {
    wecomChatId: string // 群聊系统id
    name: string // 群名称
    owner: string // 群主系统id
    createTime: number // 群创建时间
    memberList: Array<{
      type?: number // 群成员类型 (微信、企微)
      imContactId?: string // 群成员系统id
      joinTime?: number // 加入群聊时间
      joinScene?: number // 入群方式
      externalUserId?: string // 群成员企微联系人id
      identity?: 1 | 2 // 群成员身份 (枚举值)
      nickName?: string // 昵称
      avatarUrl?: string // 客户头像
    }>
    chatAvatar: string // 群头像
    imRoomId: string // 系统群聊ID
    systemTag?: Array<{
      // 群自定义标签
      [key: string]: any
    }>
    notice?: string // 群公告
  }
}

interface IExternalIdToWxIdResponse {
  errcode: number
  errmsg: string
  wxid: string
}

interface ICreateRoomResponse {
    errcode: number
    errmsg: string
    data: {
        chatId: string
        roomWxid: string
    }
}

interface Tag {
  tagName: string
  type: string
  groupName: string
  tagId: string
}


interface IGetCustomerInfoResponse {
  errcode: number
  errmsg: string
  data: {
    imContactId: string
    name: string
    avatar: string
    gender: string
    remark: string
    createTimestamp: string
    remarkMobiles: string[]
    imInfo: {
      type: string
      tags: Tag[]
      externalUserId: string
      unionId: string
      followUser: {
        wecomUserId: string
        state: string
      }
    }
    botInfo: any
    systemTags?: any
  }
}

interface AddToGroupParams {
  botUserId: string
  contactWxid: string // 客户的 imContactId
  roomWxid: string // 群聊的 wxId
}

interface RemoveFromGroupParams {
  imBotId: string
  contactWxid: string // 客户的 imContactId
  roomWxid: string // 群聊的 wxId
}

interface IJuziSendMsgParam {
  imBotId: string
  imRoomId?: string
  imContactId?: string
  msg: IWecomMessage
  isAtAll?: boolean
  isAnnouncement?: boolean

  externalRequestId?: string
}

interface GetGroupDetailParams {
  imBotId: string
  imRoomId: string
}

interface IsInGroupParams {
  imBotId: string
  imRoomId: string
  imContactId: string
}

interface IAddFriendParam {
  imBotId: string
  phone: string
  hello: string
  type?: 1 | 3 // 1 个微，3 企微
}

interface IAddToGroupResponse {
  errcode?: number
  errmsg?: string
}

interface IRemoveFromGroupParams {
  imBotId: string
  contactWxid: string
  roomWxid: string
}

/**
 * 句子接口，注意要自己处理报错
 */
export class JuziAPI {
  public static token = Config.setting.juziWecom.token

  public static async addFriendByPhone(param: IAddFriendParam) {
    const url = 'v2/customer/addByPhone'
    const client = new JuZiWecomClient()
    return await client.post(url, param)
  }

  public static async getOriginalImage(chatId: string, messageId: string) {
    const url = new URL('message/getArtworkImage', Config.setting.juziWecom.orgBaseUrl).toString()

    try {
      const response = await axios.post(url, {
        token: Config.isOnlineTestAccount() ? '661cfbe507ea8c7bacfe5c8c' : Config.setting.wechatConfig?.orgToken as string,
        chatId,
        messageId
      })


      return response.data
    } catch (error) {
      console.error('请求失败:', error)
      throw error
    }
  }

  public static async createRoom(createGroupParams: CreateGroupParams) {
    const url = 'v1/instantReply/createRoom'
    const client = new JuZiWecomClient()

    return await client.post(url, createGroupParams)
  }

  public static async addToGroup(params: AddToGroupParams) {
    const url = 'v1/instantReply/addFromRoom'
    const client = new JuZiWecomClient()

    const response =  await client.post<IAddToGroupResponse>(url, params)

    return response.data
  }

  /**
   * 查询群详情
   */
  public static async groupDetail(params: GetGroupDetailParams) {
    const url = 'v2/groupChat/detail'
    const client = new JuZiWecomClient()

    return await client.get<IGroupInfoData>(url, params)
  }

  public static async isInGroup(param: IsInGroupParams) {
    const response = await this.groupDetail(param)
    const data = response.data as IGroupInfoData

    if (data && data.errcode === 0 && data.data) {
      return data.data.memberList.some((item) => item.imContactId === param.imContactId)
    } else {
      console.log('查询是否在群中失败,err:', JSON.stringify(data))
    }

    return false
  }

  public static async listCustomers(imBotId: string) {
    const url = 'v2/customer/list'
    const client = new JuZiWecomClient()
    const response = await client.get(url, {
      current: 2,
      pageSize: 200,
      imBotId
    })

    return response.data
  }

  public static async sendMsg(param: IJuziSendMsgParam) {
    if (!param.imContactId && !param.imRoomId) {
      throw new Error('imContactId 和 imRoomId 不能同时为空')
    }

    const imBotId = param.imBotId
    const externalRequestId = param.externalRequestId ?? UUID.short()
    const url = 'v2/message/send'
    const messageType = param.msg.type

    return await new JuZiWecomClient().post(url, {
      imBotId,
      externalRequestId,
      imContactId: param.imContactId,
      imRoomId: param.imRoomId,
      messageType: messageType,
      payload: param.msg,
      isAtAll: param.isAtAll,
      isAnnouncement: param.isAnnouncement
    })
  }


  public static async sendGroupMsg(from: string, groupId: string, msg: IWecomMessage) {
    const imBotId = from
    const externalRequestId = UUID.short()
    const url = 'v2/message/send'
    const messageType = msg.type

    return await new JuZiWecomClient().post(url, {
      imBotId,
      externalRequestId,
      imRoomId: groupId,
      messageType: messageType,
      payload: msg
    })
  }

  static async externalIdToWxId(wx_id: string) {
    const url = 'v1/customer/externalUserId_to_wxid'

    const client = new JuZiWecomClient()
    const response = await client.get<IExternalIdToWxIdResponse>(url, {
      externalUserId: wx_id,
      imBotId: Config.setting.wechatConfig?.id
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.wxid
    }

    return null
  }

  static async getMembersList(current: number, pageSize: number) {
    const url = 'v1/user/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse >(url, {
      current,
      pageSize
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  static async getCustomerInfo(botId: string, customerId: string) {
    const url = 'v2/customer/detail'
    const client = new JuZiWecomClient()
    const response = await client.post<IGetCustomerInfoResponse>(url, {
      systemData: {
        imBotId: botId,
        imContactId: customerId
      }
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  static async getMemberInfo(customerId: string) {
    const url = 'v2/user/detail'
    const client = new JuZiWecomClient()
    const response = await client.get<IGetCustomerInfoResponse>(url,
      {
        uid: customerId
      }
    )

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }


  /**
   * 这个接口有多账号 externalUserId 不一致的风险，采用 customer/detail 接口
   * @param user_id
   */
  static async wxIdToExternalUserId(user_id: string) {
    const customerInfo = await this.getCustomerInfo(Config.setting.wechatConfig?.id as string, user_id)

    if (customerInfo) {
      return customerInfo.imInfo.externalUserId
    }

    return null
  }

  static async updateUserAlias(botId: string, userId: string, newAlias: string) {
    const url = 'v2/customer/updateContactRemark'

    const client = new JuZiWecomClient()
    const response = await client.post<ICommonResponse>(url, {
      imBotId: botId,
      imContactId: userId,
      remark: newAlias
    })

    if (response.data && response.data.errcode === 0) {
      return response.data
    }

    return null
  }

  static async getTags() {
    const url = 'v2/tag/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse>(url, {})

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  /**
   * https://apifox.com/apidoc/shared-71b1c1e2-7473-4473-9e25-d2091b22199e/api-147790387
   * @param externalUserId
   * @param botUserId
   * @param addTags 标签 ID
   * @param removeTags 标签 ID
   */
  static async updateUserTags(externalUserId: string, botUserId: string, addTags: string[], removeTags: string[]) {
    const url = 'v2/tag/mark'

    const client = new JuZiWecomClient()
    const response = await client.post<ICommonResponse>(url, {
      externalUserId,
      botUserId: botUserId,
      addTagId: addTags,
      removeTagId: removeTags
    })

    if (response.data && response.data.errcode === 0) {
      return response.data
    }

    return null
  }


  static async getGroups() {
    const url = 'v2/group/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse>(url, {})

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  static async addPhoneNumber(imBotId: string, imContactId: string, phoneNumbers: string[]) {
    const url = 'v2/customer/addContactPhoneNumbers'
    const client = new JuZiWecomClient()
    const response = await client.post<ICommonResponse>(url, {
      imBotId,
      imContactId,
      addPhoneNumbers: phoneNumbers,
      overrideExistPhoneNumbers: true
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  static async getCustomerPhoneNumber(botId: string, customerId: string) {
    const customerInfo = await this.getCustomerInfo(botId, customerId)
    if (customerInfo) {
      return customerInfo.remarkMobiles
    }

    return null
  }

  static async listGroup(imBotId: string) {
    const url = 'v2/groupChat/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse>(url, {
      current: 1,
      pageSize: 100,
      imBotId
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  public static async removeFromGroup(params: RemoveFromGroupParams) {
    const url = 'v1/instantReply/removeFromRoom'
    const client = new JuZiWecomClient()
    const response = await client.post<ICommonResponse>(url, params)

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    } else {
      console.log('群聊踢人失败', response.data.errcode, response.data.errmsg, JSON.stringify(params, null, 2))
      return null
    }
  }

  public static async kickNonWhitelistMembers(imBotId: string, imRoomId: string) {
    if (!imBotId || !imRoomId) {
      throw new Error('imBotId and imRoomId are required')
    }

    // 读取白名单配置文件
    const whitelistFilePath = path.join(__dirname, '../../config/whitelist.json')
    const whitelistData = JSON.parse(fs.readFileSync(whitelistFilePath, 'utf-8'))
    const whitelist = whitelistData.map((item: any) => item.imContactId)

    try {
      const groupDetail = await this.groupDetail({ imBotId, imRoomId })
      const data = groupDetail.data as IGroupInfoData
      logger.log('groupDetail', JSON.stringify(data.data.memberList, null, 2))

      if (data && data.errcode === 0 && data.data) {
        const members = data.data.memberList
          .filter((member) =>
            member.imContactId && !whitelist.includes(member.imContactId) && member.imContactId !== imBotId
          ) // 移除掉白名单中的客户

        // 漏斗式重试机制
        let remainingMembers = members.map((member) => ({
          imBotId,
          contactWxid: member.imContactId as string,
          roomWxid: imRoomId
        }))

        const maxRetries = 6
        for (let retryCount = 0; retryCount < maxRetries && remainingMembers.length > 0; retryCount++) {
          // 第一次不延迟，后续按照时间间隔延迟
          if (retryCount > 0) {
            const delayTime = this.getRetryDelay(retryCount)
            logger.log(`第${retryCount}次重试，延迟${delayTime / 1000}秒，剩余${remainingMembers.length}个成员需要处理`)
            await sleep(delayTime)
          }

          // 处理当前批次
          const chunks = this.chunkArray(remainingMembers, 10)
          const failedMembers: IRemoveFromGroupParams[] = []

          let batchIndex = 0
          for (let i = 0; i < chunks.length; i++) {
            const chunk = chunks[i]
            batchIndex += 1

            const results = await Promise.all(chunk.map(async (params) => {
              const result = await this.removeFromGroup(params)
              return { params, result }
            }))

            let failedCount = 0

            // 收集失败的成员
            results.forEach(({ params, result }) => {
              if (result === null) {
                failedCount += 1
                failedMembers.push(params)
              }
            })

            // 日志输出：带上批次号
            logger.log(`第${batchIndex}批，尝试踢出 ${chunk.length} 人，失败 ${failedCount} 人`)

            await randomSleep(1000, 3000) // 踢完一批随机等待下
          }

          // 更新剩余需要处理的成员
          remainingMembers = failedMembers

          // 如果没有失败的成员，提前退出循环
          if (remainingMembers.length <= 10) {
            logger.log(`所有成员已成功移除，在第${retryCount + 1}次尝试后完成`)
            break
          }
        }

        // 记录最终未能成功移除的成员
        if (remainingMembers.length > 0) {
          logger.warn(`经过${maxRetries}次尝试后，仍有${remainingMembers.length}个成员未能成功移除`)
          logger.warn('未能移除的成员:', JSON.stringify(remainingMembers, null, 4), remainingMembers.length)
        }
      }
    } catch (error) {
      console.error('Error kicking non-whitelist members:', error)
      throw new Error('Failed to kick non-whitelist members')
    }
  }

  private static getRetryDelay(retryCount: number): number {
    switch (retryCount) {
      // case 1: return 60 * 1000  // 1分钟
      // case 2: return 2 * 60 * 1000 // 2 分钟
      // case 3: return 3 * 60 * 1000 // 3 分钟
      // case 4: return 5 * 60 * 1000 // 5 分钟
      default: return 60 * 1000    // 默认30秒
    }
  }

  private static chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  public static async changeRoomName(param: IChangeRoomNameParam) {
    if (!param.imRoomId || !param.imBotId || !param.name) {
      throw new Error('token, imRoomId, imBotId, and name are required')
    }

    const url = 'v1/instantReply/changeRoomName'  // The API URL for changing room name
    let all_groups: any[]

    try {
      all_groups = await JuziAPI.listGroup(param.imBotId)
      logger.log('oriRoomName', all_groups.find((group: { imRoomId: string }) => group.imRoomId === param.imRoomId).name)
      await new JuZiWecomClient().post(url, {
        imRoomId: param.imRoomId,
        imBotId: param.imBotId,
        name: param.name
      })
      all_groups = await JuziAPI.listGroup(param.imBotId)
      logger.log('newRoomName', all_groups.find((group: { imRoomId: string }) => group.imRoomId === param.imRoomId).name)
    } catch (error) {
      console.error('Error changing room name:', error)
      throw new Error('Failed to change room name')
    }
  }

  // 群聊同步接口
  public static async groupSync(imBotId: string) {
    if (!imBotId) {
      throw new Error('imBotId is required when group sync')
    }
    const url = 'v2/groupChat/syncRoom'
    try {
      return await new JuZiWecomClient().post(url, { imBotId: imBotId })
    } catch (error) {
      console.error('Error sync room:', error)
      throw new Error('Failed to sync room')
    }
  }
}