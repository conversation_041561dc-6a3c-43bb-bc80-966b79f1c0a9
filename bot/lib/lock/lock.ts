import { RedisCacheDB } from '../../model/redis/redis_cache'
import RedLock from 'redlock'

export class AsyncLock {
  private redLock: RedLock

  constructor(options?: {
        retryCount?: number
        retryDelay?: number
    }) {
    this.redLock = new RedLock([RedisCacheDB.getInstance()], {
      retryCount: options?.retryCount ?? 300,
      retryDelay: options?.retryDelay ?? 1000,
    })
  }

  /**
   * 获取分布式锁并执行任务
   * @param key 锁住的资源名称
   * @param fn 需要锁住的执行函数
   * @param opts
   *
   * @example
   * const lock = new DistributedLock();
   *
   * // 回调方式
   * lock.acquire(
   *   'resourceKey',
   *   (done) => {
   *     // 异步任务
   *     done(null, result);
   *   },
   *   (err, result) => {
   *     // 处理结果
   *   },
   *   { ttl: 5000 }
   * );
   */
  async acquire<T>(
    key: string,
    fn: any,
    opts?: {timeout: number} // 毫秒
  ): Promise<T> {
    const ttl = opts?.timeout ?? 5000
    let lock
    try {
      lock  = await this.redLock.acquire([key], ttl)
    } catch (e) {
      console.log('ignore error from acquire:', e)
    }

    try {
      return await fn()
    } finally {
      try {
        if (lock) {
          await lock.release()
        }
      } catch (e) {
        console.log('ignore error from release:', e)
      }
    }
  }
}
