import removeMarkdown from 'markdown-to-text'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { MoerAPI } from '../../../../model/moer_api/moer'
import { PrismaMongoClient } from '../../../../model/mongodb/prisma'
import { ChatDB } from '../../database/chat'
import { DataService } from '../../getter/getData'
import { ChatStateStore, ChatStatStoreManager, IUserSlot } from '../../storage/chat_state_store'
import { ChatHistoryService } from '../chat_history/chat_history'
import { inviteToGroup } from '../flow/helper/inviteToGroup'
import { ExtractUserSlotsV2 } from '../flow/helper/slotsExtract'
import { MessageSender } from '../message/message_send'
import { ContextBuilder } from '../agent/context'

export const conditionJudgeMap:Record<string, ((params:{chatId:string;userId:string})=>Promise<boolean>)> = {
  '进群':async({ userId }) => {
    const isInGroup = await DataService.isInGroup(userId)
    return isInGroup
  },
  '先导课到课': async({ chatId }) => {
    return await DataService.isInClass(chatId, { day:0 })
  },
  '第一节课到课': async({ chatId }) => {
    return await DataService.isInClass(chatId, { day:1 }) || DataService.isInClass(chatId, { day:1, is_recording:true })
  },
  '第二节课到课': async({ chatId }) => {
    return await DataService.isInClass(chatId, { day:2 }) || DataService.isInClass(chatId, { day:2, is_recording:true })
  },
  '第三节课到课': async({ chatId }) => {
    return await DataService.isInClass(chatId, { day:3 }) || DataService.isInClass(chatId, { day:3, is_recording:true })
  },
  '第四节课到课': async({ chatId }) => {
    return await DataService.isInClass(chatId, { day:4 }) || DataService.isInClass(chatId, { day:4, is_recording:true })
  },
  '先导课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, { day:0 })
  },
  '第一节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, { day:1 }) || DataService.isCompletedCourse(chatId, { day:1, is_recording:true })
  },
  '第二节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, { day:2 }) || DataService.isCompletedCourse(chatId, { day:2, is_recording:true })
  },
  '第三节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, { day:3 }) || DataService.isCompletedCourse(chatId, { day:3, is_recording:true })
  },
  '第四节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, { day:4 }) || DataService.isCompletedCourse(chatId, { day:4, is_recording:true })
  },
  '先导课看了一秒': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanOneSecond(chatId, { day:0 })
  },
  '第一节课看了一秒': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanOneSecond(chatId, { day:1 }) || DataService.isAttendCourseMoreThanOneSecond(chatId, { day:1, is_recording:true })
  },
  '第二节课看了一秒': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanOneSecond(chatId, { day:2 }) || DataService.isAttendCourseMoreThanOneSecond(chatId, { day:2, is_recording:true })
  },
  '第三节课看了一秒': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanOneSecond(chatId, { day:3 }) || DataService.isAttendCourseMoreThanOneSecond(chatId, { day:3, is_recording:true })
  },
  '第四节课看了一秒': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanOneSecond(chatId, { day:4 }) || DataService.isAttendCourseMoreThanOneSecond(chatId, { day:4, is_recording:true })
  },
  '完成四节课':async({ chatId }) => {
    return await DataService.isCompletedAllCourse(chatId)
  },
  '已下单': async({ chatId }) => {
    const isPaid = await DataService.isPaidSystemCourse(chatId)
    return isPaid
  },
  '能量测评完成': async ({ chatId }) => {
    const state = ChatStateStore.getFlags(chatId)
    return Boolean(state.is_complete_energy_test_analyze) || Boolean(state.is_complete_energy_test)
  },
  '财富果园解读': async({ chatId }) => {
    const state = ChatStateStore.getFlags(chatId)
    return Boolean(state.is_complete_wealth_orchard_analyze)
  },
  '对话过': async({ chatId }) => {
    const userMessageCount = await ChatHistoryService.getUserMessageCount(chatId)
    return userMessageCount > 0
  },
  '对话过三轮以上': async ({ chatId }) => {
    const userMessageCount = await ChatHistoryService.getUserMessageCount(chatId)
    return userMessageCount > 3
  },
  '时间到上课周': async({ chatId }) => {
    const time = await DataService.getCurrentTime(chatId)
    return Boolean(time.is_course_week)
  },
  '完成挖需': async({ chatId }) => {
    const userSlots = await ExtractUserSlotsV2.extractUserSlots(chatId, 6, { chat_id:chatId })
    return userSlots.isTopicSubTopicExist('基本信息', '生活角色') || userSlots.isTopicExist('过往冥想经验') || userSlots.isTopicExist('痛点') || userSlots.isTopicExist('冥想目标')
  },
  '有手机号': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const info = await mongoClient.chat.findFirst({ where:{ id:chatId }, select:{ chat_state:true } })
    if (info?.chat_state.userSlots?.['phoneNumber']) {
      return true
    }
    return false
  },
  '四节课都看完或者只看了第三节课或只看了第四节课': async({ chatId }) => {
    const day1Complete = await DataService.isCompletedCourse(chatId, { day:1 }) || await DataService.isCompletedCourse(chatId, { day:1, is_recording:true })
    const day2Complete = await DataService.isCompletedCourse(chatId, { day:2 }) || await DataService.isCompletedCourse(chatId, { day:2, is_recording:true })
    const day3Complete = await DataService.isCompletedCourse(chatId, { day:3 }) || await DataService.isCompletedCourse(chatId, { day:3, is_recording:true })
    const day4Complete = await DataService.isCompletedCourse(chatId, { day:4 }) || await DataService.isCompletedCourse(chatId, { day:4, is_recording:true })

    return (day1Complete && day2Complete && day3Complete && day4Complete) || (!day1Complete && !day2Complete && day3Complete && !day4Complete) || (!day1Complete && !day2Complete && !day3Complete && day4Complete)
  },
  '腾讯投放3元客户': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    const source = userInfo?.chat_state.userSlots as IUserSlot|null
    return source?.source == '腾讯投放3元'
  },
  '抖音投放6元客户': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    const source = userInfo?.chat_state.userSlots as IUserSlot|null
    return source?.source == '抖音投放6元'
  },
  '保定私域0元客户': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    const source = userInfo?.chat_state.userSlots as IUserSlot|null
    return source?.source == '保定私域0元'
  },
  '腾讯投放1元客户': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    const source = userInfo?.chat_state.userSlots as IUserSlot|null
    return source?.source == '腾讯投放1元'
  },
  '广点通投放0元客户': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    const source = userInfo?.chat_state.userSlots as IUserSlot|null
    return source?.source == '广点通投放0元'
  },
  '小红书投放0元客户': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    const source = userInfo?.chat_state.userSlots as IUserSlot|null
    return source?.source == '小红书投放0元'
  },
  '百度投放1元客户': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    const source = userInfo?.chat_state.userSlots as IUserSlot|null
    return source?.source == '百度投放1元'
  },
}

export const textVariableMap:Record<string, (params:{chatId:string;userId:string})=> Promise<string>> = {
  '客户昵称': async({ chatId }) => {
    const chat = await ChatDB.getById(chatId)
    if (!chat) {
      throw ('找不到这个客户')
    }
    return chat.contact.wx_name
  },
  '报名链接': async() => {
    return 'https://t.meihao.com/HhYJ'
  },
  '先导课上课链接':async({ chatId }) => {
    return await DataService.getCourseLink(0, chatId, false)
  },
  '第一节课上课链接':async({ chatId }) => {
    return await DataService.getCourseLink(1, chatId, false)
  },
  '第一节课录播链接':async({ chatId }) => {
    return await DataService.getCourseLink(1, chatId, true)
  },
  '第二节课上课链接':async({ chatId }) => {
    return await DataService.getCourseLink(2, chatId, false)
  },
  '第二节课录播链接':async({ chatId }) => {
    return await DataService.getCourseLink(2, chatId, true)
  },
  '第三节课上课链接':async({ chatId }) => {
    return await DataService.getCourseLink(3, chatId, false)
  },
  '第三节课录播链接':async({ chatId }) => {
    return await DataService.getCourseLink(3, chatId, true)
  },
  '第四节课上课链接':async({ chatId }) => {
    return await DataService.getCourseLink(4, chatId, false)
  },
  '第四节课录播链接':async({ chatId }) => {
    return await DataService.getCourseLink(4, chatId, true)
  },
  '188课程课程链接': async() => {
    const link = (await MoerAPI.get188CourseInfo()).shortLink
    return link
  },
  '手机号': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const info = await mongoClient.chat.findFirst({ where:{ id:chatId }, select:{ chat_state:true } })
    if (info?.chat_state.userSlots?.['phoneNumber']) {
      return info.chat_state.userSlots['phoneNumber'] as string
    }
    throw ('没有手机号')
  }
}

export const actionCustomMap:Record<string, (params:{chatId:string;userId:string})=> Promise<void>> = {
  '拉群': async({ userId }) => {
    await inviteToGroup(userId)
  },
  '发送原版分手信': async({ chatId, userId }) => {
    await ChatStatStoreManager.initState(chatId)
    const userPortrait = await ContextBuilder.getCustomerPortrait(chatId)
    const name = await DataService.getWechatName(userId)
    const llmRes = await LLM.predict(`# 分手信
你是麦子老师，刚刚花费了一周多的时间服务了入门营客户${name}，但是他没有下单21天系统课，你感到非常遗憾。
你需要根据客户画像写一封分手信，希望客户能够看到信后非常感动，来找你买冥想课，你要用各种心里技巧，把真诚淋漓尽致的展现出来，用些emoji表情，用真诚打动客户！

## 你需要大致分下面几个部分完成分手信，也可以根据客户情况进行删改：
1. 一句话主题开头，直击人心
  如：XXX，我感受到您的疲惫与渴望❤️
2. 看见：结合客户分享与经历，深度共情
  如：每次想到您跟我分享的生活状态，我的心都会揪一下。工作到凌晨3点的疲惫、无法兼顾时间的压力，以及在冥想中流下的眼泪，这些都让我看到了一个努力坚持但也背负着巨大压力的您🥹。
3. 鼓励：肯定客户的努力，愿意尝试冥想
  如：作为一个陪伴您的朋友和指导者，我真的为您心疼，同时也感到敬佩——您在如此忙碌的生活中，依然愿意尝试冥想，寻找属于自己的内在平静和智慧🌟。
4. 转折：结合客户痛点推荐系统课
  如：我知道，时间对您来说非常珍贵，推掉工作来上课可能让您感到挣扎。但XXX，您可曾想过，这门课不仅仅是一个“任务”，它或许是一把钥匙，可以打开一个充满能量和智慧的世界，让您用更从容的心态面对生活的挑战💪。还记得您提到的那些流泪的时刻吗？那些不是脆弱，而是一种释放，一种灵魂深处的触动。或许正是在这些瞬间，您的内心深处在告诉您：是时候为自己而活了。
5. 深表遗憾，并尊重选择
  如：我真的很遗憾[流泪]，没能更好地帮助您找到一个更轻松的方式融入这段旅程。如果这一刻，您仍然觉得需要更多时间或空间，我完全理解，并尊重您的选择❤️。
6. 重回主题，强推系统班
  如：但我也想说，这门课是一个工具，而不是一种负担。它可以随着您的节奏而调整，而我的存在，就是为了帮助您找到适合您的方式去探索它🙌。最后，我只想对您说：您并不孤单。无论您是否继续这门课程，我都会在这里，为您提供支持，帮助您走向更平静、更觉知的未来。XXX，如果您有任何问题，我随时都在，等您的一句话❤️。
7. 落款，结合客户经历，一句话祝福+麦子老师
  如：------愿您永远找到属于自己的光🙏，麦子老师

## 客户昵称
${name}

## 客户画像
${userPortrait}
请根据以上信息完成任务！千万不要输出每一部分的标题，只输出内容即可，并用一句话主题开始：`)
    await MessageSender.sendById({
      chat_id: chatId,
      user_id: userId,
      ai_msg: removeMarkdown(llmRes)
    }, {
      shortDes: '课程结营后给客户发送的走心祝福类信件'
    })

  }
}

export const linkSourceVariableTagMap:Record<string, (params:{chatId:string;userId:string})=>Promise<string>> = {
}