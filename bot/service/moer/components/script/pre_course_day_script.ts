import { ISend<PERSON>rapper, <PERSON><PERSON><PERSON>Function, ScriptMediaFunction } from './script'
import { ISendMedia } from '../schedule/type'
import { IWecomMsgType } from '../../../../lib/juzi/type'
import { DataService } from '../../getter/getData'

export interface IPreCourseDayScript {
    energy_test_3_image: ISendMedia
    self_introduction: ISendWrapper<ScriptFunction>
    self_introduction_image: ISendMedia
    pre_course: ISendWrapper<ScriptFunction>
    pre_course_v2: ISendWrapper<ScriptFunction>
    pre_course_1: ISendWrapper<string>
    complete_pre_course1_v2: ISendWrapper<string>
    query_phone_number: ISendWrapper<string>
    complete_pre_course1: ISendWrapper<string>
    complete_pre_course2: ISendMedia
    complete_pre_course3: ISendWrapper<string>
    energy_test_1: ISendWrapper<string>
    energy_test_2: ISendWrapper<string>
    energy_test_3: ISendWrapper<string>

    pre_course_push_day2_1_video: ISendMedia
    pre_course_push_day2_1_voice: ISendMedia
    pre_course_push_day2_1_link: ISendWrapper<ScriptFunction>

    pre_course_push_day3_link: ISendWrapper<ScriptFunction>
    pre_course_push_day3_emoticon: ISendMedia

    pre_course_push_day4_text: ISendWrapper<ScriptFunction>
    pre_course_push_day4_link_card: ScriptMediaFunction

    pre_course_push_day4_1650: ISendWrapper<string>
    pre_course_push_day4_1650_link_card: ISendMedia
    pre_course_push_day4_1650_link_card_2: ISendMedia
    pre_course_push_day4_1650_pre_course_link: ISendWrapper<ScriptFunction>


    pre_course_push_day5: ISendWrapper<ScriptFunction>

    pre_course_push_day6_interaction_over_3: ISendWrapper<ScriptFunction>
    pre_course_push_day6_interaction_less_3: ISendWrapper<string>

    pre_course_day6_1936: ISendWrapper<string>
    pre_course_day6_1936_link_card: ScriptMediaFunction
    pre_course_day6_1936_image: ISendMedia

    pre_course_push_8: ISendWrapper<ScriptFunction>
    pre_course_push_9: ISendWrapper<ScriptFunction>
    pre_course_push_9_link: ISendWrapper<ScriptFunction>
    pre_course_push_10: ISendWrapper<ScriptFunction>
    pre_course_push_course_week_1: ISendWrapper<ScriptFunction>
    complete_energy_test_1: ISendWrapper<string>
    complete_energy_test_2: ISendWrapper<string>
    complete_energy_test_3: ISendMedia
    pre_course_complete_reward_mp3: ISendMedia
    app_download_remind: ISendWrapper<string>
    opening_ceremony_class_meeting_summary_part1: ISendWrapper<string>
    opening_ceremony_class_meeting_summary_part2: ISendWrapper<ScriptFunction>

    t_plus_1_7_14: ISendWrapper<ScriptFunction>
    t_plus_2_07_30_voice: ISendMedia
    t_plus_2_07_30_text: ISendWrapper<ScriptFunction>
    t_plus_3_7_03: ISendWrapper<ScriptFunction>
    t_plus_4_16_32_voice: ISendMedia
    t_plus_4_16_32_text: ISendWrapper<ScriptFunction>
    t_plus_3_with_no_interaction: ISendMedia

}

export function getPreCourseDayScript(): IPreCourseDayScript {
  return {
    self_introduction: {
      description: '自我介绍',
      content: async (userName: string, counselorName: string, startTime: string) => {
        return `您好呀。

我是${counselorName}，看到您已报名 【五天冥想入门营】
咱们课程【下周一（${startTime}）20:00】开始，连续3天晚上直播课。

❤ 这个入门营每位同学只有一次学习机会，一定珍惜哦！`
      },
    },
    self_introduction_image: {
      description: '课程安排图片',
      msg: {
        type: IWecomMsgType.Image,
        url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E8%AF%BE%E7%A8%8B%E8%A1%A8.PNG'
      },
    },
    pre_course: {
      description: '推小讲堂',
      content: async (chat_id: string) => {
        const liveLink = await DataService.getCourseLink(0, chat_id)
        return `不知道咱们对冥想了不了解，老师带的很多学员一上来都搞不清楚具体是什么。所以，唐宁老师特备为大家准备了6分钟小讲堂:
${liveLink}
（用下单手机号登录）
【一定要看哦，对后面学习很有帮助！】`
      },
    },
    pre_course_v2: {
      description: '推小讲堂',
      content: async (chat_id: string) => {
        const liveLink = await DataService.getCourseLink(0, chat_id)
        // const liveLink = await DataService.getCourseLinkByCourseNo(DataService.getNextWeekCourseNo(), 0)
        return `[课前必做]唐宁老师准备了预热小讲堂：
${liveLink}
（注意用下单手机号登录）`
      },
    },
    pre_course_1: {
      description: '推小讲堂1',
      content: '看完班班会给咱们发入学礼[礼物]',
    },

    query_phone_number: {
      description: '询问手机号',
      content: '咱们下单手机号是哪个呀，给咱们开通下课程权限',
    },
    complete_pre_course1: {
      description: '小讲堂完成后提醒',
      content: `看到咱们这边是已经完成小讲堂的学习了，非常棒哈[强]

后续我们等待【周一晚上八点】的直播新课【情绪减压冥想+沉浸秒睡冥想】`,
    },
    complete_pre_course1_v2: {
      description: '小讲堂完成后提醒',
      content: '看到咱们完成小讲堂啦，送您「冥想入门指南」可以着重看下第二部分冥想的姿势，下周一更快进入状态哦!'
    },
    complete_pre_course2: {
      description: '冥想练习指南图片',
      msg: {
        type: IWecomMsgType.Image,
        url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E5%85%A5%E9%97%A8%E7%A4%BC.jpg'

      }
    },

    complete_pre_course3: {
      description: '小讲堂完成后发放奖励',
      content: '上图是完成小讲堂的入学礼：冥想练习指南[礼物]，可以仔细看下我们的冥想准备工作哦。',
    },
    energy_test_1: {
      description: '推能量测试1',
      content: `小讲堂老师说的能量测评的使用链接在这里哦。可以帮助评估下咱们的能量状态
https://jsj.top/f/W8ktas（3分钟可以搞定）`,
    },
    energy_test_2: {
      description: '推能量测试2',
      content: '做完老师会一一给咱们解读的哦。',
    },
    energy_test_3: {
      description: '推能量测试3',
      content: `小讲堂老师说的能量测评的使用链接在这里:https://jsj.top/f/W8ktas
      
可以评估下自己当下的能量状态哦。做完班班会给咱们1V1解读下哈。
（按照自己内心第一选择就可以啦）`,
    },

    energy_test_3_image: {
      description: '能量测试图片',
      msg:  {
        type: IWecomMsgType.Image,
        url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E8%83%BD%E9%87%8F%E6%B5%8B%E8%AF%84.png'
      }
    },

    pre_course_push_day2_1_video: {
      description: '开课须知短视频',
      msg: {
        type: IWecomMsgType.VideoChannel,
        avatarUrl: 'http://wx.qlogo.cn/finderhead/DxncLlErbRFfH8SzUPHHyH6B4CibBoRkxpoFGmawibSm8t4aYgmV5MmpGfhick2DSlrmcdFGfyfxLk/0',
        coverUrl: 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaeaZZQtHYsqxzwgWRybrVopNib077e9h0mkFS13JJX8WBKpDUfKZ0TxcapuHmmMS3ss35GtyeNvKaweMorOH6YTg&token=6xykWLEnztJALj7BIfsmA9ARibE8o2q37mCCcRT9QI7vunO1chQQbHp5wBLEHkJqYTvxicggD5VerH4fO5aL7uTntAuiaGb2PGY2RBY3NMGXvhygnqyBaF7Vic9SqGgvdVsw&idx=1&dotrans=0&hy=SZ&m=&scene=2&uzid=2&finder_expire_time=1730975310&finder_eid=export%2FUzFfAgtgekIEAQAAAAAA6usvDWcBaAAAAAstQy6ubaLX4KHWvLEZgBPE1qMEJw50XtaKzNPgMIKl2fWTadXW83cxTAiJSDnA',
        description: '欢迎大家加入墨尔大家庭，一起学习成长~',
        extras: '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',
        feedType: 4,
        nickname: '墨尔智慧',
        thumbUrl: 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaeaZZQtHYsqxzwgWRybrVopNib077e9h0mkFS13JJX8WBKpDUfKZ0TxcapuHmmMS3ss35GtyeNvKaweMorOH6YTg&token=6xykWLEnztJALj7BIfsmA9ARibE8o2q37mCCcRT9QI7vunO1chQQbHp5wBLEHkJqYTvxicggD5VerH4fO5aL7uTntAuiaGb2PGY2RBY3NMGXvhygnqyBaF7Vic9SqGgvdVsw&idx=1&dotrans=0&hy=SZ&m=&scene=2&uzid=2&finder_expire_time=1730975310&finder_eid=export%2FUzFfAgtgekIEAQAAAAAA6usvDWcBaAAAAAstQy6ubaLX4KHWvLEZgBPE1qMEJw50XtaKzNPgMIKl2fWTadXW83cxTAiJSDnA',
        url: 'https://channels.weixin.qq.com/web/pages/feed?eid=export%2FUzFfAgtgekIEAQAAAAAA6usvDWcBaAAAAAstQy6ubaLX4KHWvLEZgBPE1qMEJw50XtaKzNPgMIKl2fWTadXW83cxTAiJSDnA'
      }
    },

    pre_course_push_day2_1_voice:  {
      description: '催小讲堂语音',
      msg: {
        type: IWecomMsgType.Voice,
        voiceUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/tongtongVoice/%E5%86%A5%E6%83%B3%E5%88%B0%E5%BA%95%E4%B8%BA%E4%BB%80%E4%B9%88%E8%83%BD%E5%B8%AE%E5%8A%A9%E6%88%91%E4%BB%AC%E4%BB%80%E4%B9%88.silk',
        duration: 10
      }
    },

    pre_course_push_day2_1_link:   {
      description: 'day2_小讲堂链接',
      content: async (chat_id: string) => {
        const courseLink = await DataService.getCourseLink(0, chat_id)

        return `小讲堂链接 ${courseLink}`
      }
    },


    pre_course_push_day3_link: {
      description: 'day3_小讲堂链接',
      content: async (chat_id: string) => {
        const courseLink = await DataService.getCourseLink(0, chat_id)

        return `咱们小讲堂还没看哦！先体验下海浪冥想的快速放松
${courseLink}`
      }
    },

    pre_course_push_day3_emoticon:   {
      description: '早上好表情包',
      msg: {
        type: IWecomMsgType.Emoticon,
        imageUrl:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/emoji/%e6%97%a9%e4%b8%8a%e5%a5%bd%e8%a1%a8%e6%83%85%e5%8c%85.gif'
      }
    },

    pre_course_push_day4_text: {
      description: 'day4_小讲堂话术',
      content: async (chat_id: string) => {
        const courseLink = await DataService.getCourseLink(0, chat_id)

        return `同学当下好，看到咱们还没完成小讲堂哦。
不知道咱们是不是有失眠，常常觉得精力不够，无法专注等问题。小讲堂里唐宁老师会具体这些问题的成因。
理解完这个逻辑对后续学习很有帮助哦，一定要看看：${courseLink}`
      }
    },

    pre_course_push_day4_link_card: async (chat_id: string) => {
      const courseLink = await DataService.getCourseLink(0, chat_id)

      return {
        description: 'day4_小讲堂链接',
        msg: {
          type: IWecomMsgType.Link,
          sourceUrl: `${courseLink}`,
          title: '小讲堂链接',
          summary: '欢迎来到本次【冥想练习计划】的小讲堂！',
          imageUrl: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/b818825b-5837-4437-9f64-106fd84274be/909f5b57-7b9f-451c-900a-f72dd7aee377.jpg'
        }
      }
    },


    pre_course_push_day4_1650: {
      description: '铺垫常见问题',
      content: '班班在服务了这么多学员之后发现一般刚开始学习冥想同学都有些共性问题，班班提前整理出来了入门指南+练习过程常见问题。咱们可以收藏起来看看哈❤️'
    },

    pre_course_push_day4_1650_link_card: {
      description: '推送入门指南的链接卡片4',
      msg: {
        type: IWecomMsgType.Link,
        sourceUrl: 'https://mp.weixin.qq.com/s/ppMTg2VRehJechS3a88f3Q',
        title: '入门指南',
        summary: '九大板块专业指导，新手冥想入门手册。',
        imageUrl: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/b818825b-5837-4437-9f64-106fd84274be/909f5b57-7b9f-451c-900a-f72dd7aee377.jpg'
      }
    },

    pre_course_push_day4_1650_link_card_2: {
      description: '铺垫常见问题链接卡片4',
      msg: {
        type:IWecomMsgType.Link,
        sourceUrl: 'https://doc.weixin.qq.com/doc/w3_ATcAKwaYAEYuNZfPtjcSnuBGUiBNx?scode=AF4A8gf2AAkQ64JMys#/preview',
        title: '练习过程常见问题',
        summary: '十大创建问题针对指导',
        imageUrl: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/b818825b-5837-4437-9f64-106fd84274be/909f5b57-7b9f-451c-900a-f72dd7aee377.jpg'
      }
    },

    pre_course_push_day4_1650_pre_course_link: {
      description: '铺垫常见问题',
      content: async (chat_id: string) => {
        const liveLink = await DataService.getCourseLink(0, chat_id)
        return `咱们还没听完小讲堂，刚好可以结合冥想入门指南跟跟看小讲堂里的海浪冥想练习哈。
${liveLink}`
      }
    },


    pre_course_push_day5:  {
      description: '推小讲堂周五1',
      content:  async (chat_id: string) => {
        const courseLink = await DataService.getCourseLink(0, chat_id)
        return `同学当下好，晚上我们就建学习群咯，班班关注到咱们小讲堂落下了，这就像咱们小时候预习一样重要！ 点开花几分钟听下哦
${courseLink}`
      }
    },

    pre_course_push_day6_interaction_over_3: {
      description: '推小讲堂话术 课程链接_5 互动次数大于3',
      content: async (chat_id: string) => {
        const liveLink = await DataService.getCourseLink(0, chat_id)
        return `我发现一个问题🤭，咱们很快就开课了，同学的小讲堂还没听，${liveLink}，今天有空听一下吗？`
      }
    },
    pre_course_push_day6_interaction_less_3: {
      description: '推小讲堂话术 课程链接_5 互动次数小于3',
      content:'咱们还上课吧?注意提前看下小讲堂，避免课程进不去哦'
    },

    pre_course_day6_1936: {
      description: '推小讲堂话术 周六',
      content: '晚上好，唐宁老师小讲堂给大家准备了【海浪冥想】减压练习预热，开课前咱们先体验下哦。'
    },

    pre_course_day6_1936_link_card: async (chat_id: string) => {
      const liveLink = await DataService.getCourseLink(0, chat_id)
      return {
        description: '推小讲堂链接卡片周六7',
        msg: {
          type: IWecomMsgType.Link,
          sourceUrl: `${liveLink}`,
          title: '💗【海浪冥想】小讲堂-减压预热',
          summary: '点击进入直播间\n拍单手机号登陆',
          imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E6%B5%B7%E6%B5%AA%E5%86%A5%E6%83%B3.png'
        }
      }
    },

    pre_course_day6_1936_image:  {
      description: '海浪冥想图片',
      msg:  {
        type: IWecomMsgType.Image,
        url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E6%B5%B7%E6%B5%AA%E5%86%A5%E6%83%B3.png'
      }
    },

    pre_course_push_8: {
      description: '推小讲堂话术 周日1',
      content: async (chat_id: string) => {
        const liveLink = await DataService.getCourseLink(0, chat_id)
        return `明晚8点就正式开课啦，小讲堂很适合在安静的早晨听哦:
${liveLink}`
      }
    },
    pre_course_push_9: {
      description: '推小讲堂话术 周日2',
      content: async (chat_id: string) => {
        const liveLink = await DataService.getCourseLink(0, chat_id)
        return `明天，班班刚发完一大波入学礼，老师在提醒下咱们查看小讲堂，这个对后续课程吸收很重要哦
${liveLink}`
      }
    },
    pre_course_push_9_link:
      {
        description: '推小讲堂话术 周日下午',
        content: async (chat_id: string) => {
          const preCourseLink = await DataService.getCourseLink(0, chat_id)
          return `明天开课啦，班班刚发完一大波入学礼，老师在提醒下咱们查看小讲堂哈，不然后续开通不了课程呀
${preCourseLink}`
        }
      },
    pre_course_push_10: {
      description: '推小讲堂话术 周日3',
      content: async (chat_id: string) => {
        const liveLink = await DataService.getCourseLink(0, chat_id)
        return `老师已经发了一大波小讲堂完课礼了，还没看到咱们完成哦
${liveLink}`
      }
    },

    pre_course_push_course_week_1: {
      description: '推小讲堂话术',
      content: async (chat_id: string) => {
        const liveLink = await DataService.getCourseLink(0, chat_id)
        return `晚上8点来上课啦，最后老师来查漏补缺下，咱们要先完成小讲堂哈，晚上吸收才能biubiubiu!
${liveLink}`
      }
    },

    complete_energy_test_1: {
      description: '能量测试完成话术',
      content: `很多刚来的同学问我，冥想是什么呢?看看唐宁老师怎么说？
班班描述个我的真实场景，年过30后，家庭/工作都要背负起很多的责任，
当你想达成一个目标：孩子被养育得健康丰盛，事业上想达到一个收入目标，发现老公天天玩手机不陪伴孩子，工作伙伴也哪哪儿达不到自己的期待。

最后陷入“别人为什么总是做不到他应该做好的事情”的偏执，愤懑和焦虑中，而我也知道要求别人没有用除了徒增烦躁，需要更积极和智慧的力量，那怎么获取积极和智慧呢？

就如唐宁老师所说：冥想链接内在和外在的桥梁，向内发现平静与智慧来面对生活的际遇。`,
    },

    complete_energy_test_2: {
      description: '能量测试完成话术',
      content: '“冥想是驾起内在和外在的桥梁，让我们向内看见，拥有当下的力量。”',
    },

    complete_energy_test_3: {
      description: '冥想作用视频',
      msg: {
        type: IWecomMsgType.VideoChannel,
        'avatarUrl': 'http://wx.qlogo.cn/finderhead/S4gvmAA4RuJJoCYucthibbriargqeWpymrIaRJMkGeYjkhuffBYJbIz2BCba7m3EREGcHsrqicdfuY/0',
        'coverUrl': 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaKrrAgAiaHDO0pCOCJ21YpBwBwiag5HcqsAaKDGr8miaaUPdiaq3xfmIwXkVk6JXlRo6VZU0vYM4zFgbAIDxmwic4NqQ&token=cztXnd9GyrHZSb7Xb38ib0cCc2nx0Zrfywyd7Jblsjh9JIOECB3dQictLvdaqVXPIcXusAqQRGOcQIZ2zBB2PPx8QEIS8UvsIt2OBWPRKIy6ia8jiaXxYjuedfibZRWNIpico2&idx=1&bizid=1023&dotrans=0&hy=SZ&m=&scene=0&finder_expire_time=1726727279&finder_eid=export%2FUzFfAgtgekIEAQAAAAAArywtSwTXtgAAAAstQy6ubaLX4KHWvLEZgBPEx4FsNWQvDPSEzNPgMILjy4wAFYOewM8qDjjZB6SB',
        'description': '冥想它让你不再把目光去向外追逐，而是把你所有的专注焦点收摄回当下，收回到你的内在力量#冥想#修行#知识分享#詹唐宁',
        'extras': '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',
        'feedType': 4,
        'nickname': '墨尔冥想',
        'thumbUrl': 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaKrrAgAiaHDO0pCOCJ21YpBwBwiag5HcqsAaKDGr8miaaUPdiaq3xfmIwXkVk6JXlRo6VZU0vYM4zFgbAIDxmwic4NqQ&token=cztXnd9GyrHZSb7Xb38ib0cCc2nx0Zrfywyd7Jblsjh9JIOECB3dQictLvdaqVXPIcXusAqQRGOcQIZ2zBB2PPx8QEIS8UvsIt2OBWPRKIy6ia8jiaXxYjuedfibZRWNIpico2&idx=1&bizid=1023&dotrans=0&hy=SZ&m=&scene=0&finder_expire_time=1726727279&finder_eid=export%2FUzFfAgtgekIEAQAAAAAArywtSwTXtgAAAAstQy6ubaLX4KHWvLEZgBPEx4FsNWQvDPSEzNPgMILjy4wAFYOewM8qDjjZB6SB',
        'url': 'https://channels.weixin.qq.com/web/pages/feed?eid=export%2FUzFfAgtgekIEAQAAAAAArywtSwTXtgAAAAstQy6ubaLX4KHWvLEZgBPEx4FsNWQvDPSEzNPgMILjy4wAFYOewM8qDjjZB6SB'
      }
    },
    pre_course_complete_reward_mp3: {
      description: '海浪减压冥想音频',
      msg: {
        type: IWecomMsgType.File,
        name: '海浪减压冥想.mp3',
        url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E6%B5%B7%E6%B5%AA%E5%87%8F%E5%8E%8B%E5%86%A5%E6%83%B3.MP3'
      }
    },
    app_download_remind: {
      description: '墨尔冥想app下载提醒',
      content: '同学您可以先提前下载墨尔冥想app哈（手机打开应用商城搜索【墨尔冥想】），后面我们正式上课在app里面上课，画质和流程度是最佳的',
    },
    opening_ceremony_class_meeting_summary_part1: {
      description: '开营典礼总结 周日1',
      content: `同学，今晚群内的开营典礼完美结束了 ，下面是老师整理的班会内容重点任务，需要完成一下哈
      
Part1：墨尔冥想app👉手机打开应用商城搜索【墨尔冥想】`
    },
    opening_ceremony_class_meeting_summary_part2: {
      description: '开营典礼总结 任务2',
      content: async (chat_id: string) => {
        const preCourseLink = await DataService.getCourseLink(0, chat_id)
        return `Part2：完成小讲堂 👉${preCourseLink}（15分钟一定要完整听完哟💘已听完的可以忽略）
        
Part3：能量测试 👉https://jinshuju.net/f/YekJdS（点击进入测评，需要3分钟，然后把分数截图发送在群里，做完可以忽略）
 重要事项👇❗❗❗：\n周一到周三，每晚【20:00】直播无回放，咱们调好闹钟，不见不散哦！🙌`
      }
    },
    t_plus_1_7_14:{
      description: 't+1 7:14催小课堂',
      content: async (chat_id: string) => {
        const preCourseLink = await DataService.getCourseLink(0, chat_id)
        return `早安! 为了赶上你能看到班班的消息，早起提醒您要记得看看小讲堂哈。点这里看：${preCourseLink}`
      }
    },
    t_plus_2_07_30_voice:{
      description: 't+2 07:30 催小课堂语音',
      msg: {
        type: IWecomMsgType.Voice,
        duration: 23,
        voiceUrl:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/tongtongVoice/t-2%e5%82%ac%e4%bf%83%e5%b0%8f%e8%ae%b2%e5%a0%82.silk'
      }
    },
    t_plus_2_07_30_text:{
      description: 't+2 12:00 催小课堂文本',
      content: async (chat_id: string) => {
        const preCourseLink = await DataService.getCourseLink(0, chat_id)
        return `点这里看：${preCourseLink}`
      }
    },
    t_plus_3_7_03:{
      description: 't+3 7:03 催小课堂文本',
      content: async (chat_id: string) => {
        const preCourseLink = await DataService.getCourseLink(0, chat_id)
        return `今天又爬起来，提醒咱提前看前面发的小讲堂，看完才能解锁后续课程，不然后面您可能上不了课。
小讲堂链接：${preCourseLink}`
      }
    },
    t_plus_4_16_32_voice:{
      description: 't+4 16:32 催小课堂语音',
      msg: {
        type: IWecomMsgType.Voice,
        duration: 17,
        voiceUrl:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/tongtongVoice/T-4%e7%9a%84%e5%82%ac%e5%85%88%e5%af%bc%e8%af%be.silk'
      }
    },
    t_plus_4_16_32_text:{
      description: 't+4 16:32 催小课堂文本',
      content: async (chat_id: string) => {
        const preCourseLink = await DataService.getCourseLink(0, chat_id)
        return `点击链接就可以观看了：${preCourseLink}`
      }
    },
    t_plus_3_with_no_interaction:{
      description: 't+3 催互动 连续两天未互动',
      msg: {
        type: IWecomMsgType.Voice,
        duration: 23,
        voiceUrl:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/tongtongVoice/T-3%e8%bf%9e%e7%bb%ad%e4%b8%8d%e4%ba%92%e5%8a%a8.silk'
      }
    }
  }
}