import { MetaActionComponent } from '../meta_action_component'
import { Promise } from 'mongoose'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { FreeTalk } from '../../agent/freetalk'
import { IActionInfo, PostAction } from './post_action'
import { FreeThink } from '../../agent/freethink'


export class Introduction extends MetaActionComponent {

  isStageActive(chatId: string): Promise<boolean> {
    const nodeCount = ChatStateStore.getNodeCount(chatId, FreeTalk.name)

    return Promise.resolve(nodeCount <= 15)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '提醒完成小讲堂': PostAction.sendPreCourseLink,
    }
    return Promise.resolve(actionMap)
  }

  getBaseGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(FreeThink.metaActionIntroduction)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(FreeThink.thinkPromptIntroduction)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }

  getGuidance(chatId: string, strategy: string, actions: string[], roundId: string): Promise<string> {
    return Promise.resolve('')
  }
}