import { MetaActionComponent } from '../meta_action_component'
import { Promise } from 'mongoose'
import { IActionInfo, PostAction } from './post_action'
import { isScheduleTimeAfter } from '../../schedule/creat_schedule_task'
import { DataService } from '../../../getter/getData'
import { FreeThink } from '../../agent/freethink'
import { LLM } from '../../../../../lib/ai/llm/LLM'


export class AfterCourse4 extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    const afterCourse4 = isScheduleTimeAfter(currentTime, { is_course_week: true, day: 4, time: '22:20:00' })
    return Promise.resolve(afterCourse4)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '发起报名邀约': PostAction.sendInvitation,
      '发送学员案例': PostAction.sendCaseImage,
    }
    return Promise.resolve(actionMap)
  }

  getBaseGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(FreeThink.metaActionAfterCourse4)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(FreeThink.thinkPromptAfterCourse4)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }

  async getGuidance(chatId: string, strategy: string, actions: string[], roundId: string): Promise<string> {
    if (actions.includes('明确冥想价值')) {
      const systemCourseKnowledge = await AfterCourse4.extractSystemCourseKnowledge(strategy, roundId)
      return `\n系统课补充信息：\n${systemCourseKnowledge}`
    }
    return ''
  }


  public static async extractSystemCourseKnowledge(strategy: string, roundId: string) {
    const prompt = `# 角色
你是一位冥想销售，你正在给客户推销冥想系统班

# 系统班知识（只能引用本段内容，不得添加、臆造或引用外部信息）
- Week 1（练习定静慧基础，效果：给大脑做“除尘＋校准”，心静、情绪稳、专注力明显提升）
  - Day 1‑2禅宗五步
    - 教授内容：五步坐禅法（眼观鼻→鼻观嘴→嘴观心→结印→正脊），快速进入禅定。
    - 学习效果：注意力难集中、心浮气躁的人，练完能明显感觉大脑清明、内耗减少，做事不拖拉，能一口气完成。
  - Day 3 聆听寂静
    - 教授内容：耳根圆通——先听有声再听无声，借“空隙”入定。
    - 学习效果：长期被外界声音干扰、容易烦躁的人，能建立起“内在静场”，即使外界吵，心也不跟着跑。
  - Day 4‑5 步步莲花行禅
    - 教授内容：正念行走——一呼一吸一抬腿，体察“生‑住‑灭”。
    - 学习效果：情绪压抑、脑子容易打结的人，一边走一边顺情绪，走完感觉轻松、清楚该怎么做下一步。
  - Day 6 五步复训＋复盘
    - 教授内容：巩固前五日要领，记录觉察曲线。
    - 学习效果：对自控力差、容易被手机和碎事打断的人，能显著提升专注力，工作学习更高效、进入感强。
  - Day 7 自由组合
    - 教授内容：学员按“觉‑静‑动”顺序自选三练习，形成个人日课。
    - 学习效果：适合节奏乱、生活碎片化的人群，自选练法组合后可形成个人“稳态系统”，随时回归内在。

- Week 2（五大呼吸禅，效果：像给身心装了五块电池——安全感、水润感、动力感、灵感、定力一次性加满）
  - Day 8 地呼吸
    - 教授内容：脊柱贯地站姿呼吸，扎根与安全感训练。
    - 学习效果：适合长期焦虑、心悬空、缺乏底气的人，练完有“脚踏实地”的实感，稳住了心，也稳住了场。
  - Day 9 水呼吸
    - 教授内容：脐轮长吐法，情绪疗愈与水元素净化。
    - 学习效果：情绪敏感、容易崩的人，练完像泡了一场情绪温泉，烦躁和委屈自然软化，恢复温柔与流动感。
  - Day 10 火呼吸
    - 教授内容：小火（鼻吸鼻呼）+大火（鼻吸口呼）节奏，激活太阳轮。
    - 学习效果：拖延严重、做事总提不起劲的人，练完像身体“点了火”，行动意愿变强，当天效率倍增。
  - Day 11 风呼吸
    - 教授内容：设意图‑净气场‑风满天下，清空杂念。
    - 学习效果：思绪混乱、灵感枯竭的人练完脑中如风吹雾散，清爽通透，写作、策划、输出力明显提升。
  - Day 12 空呼吸
    - 教授内容：全息观空，构建心灵防护罩。
    - 学习效果：适合易焦虑、易被情绪控制的人，练完像内在装了“防弹玻璃”，能量稳、不容易被带跑。
  - Day 13 五禅串练
    - 教授内容：地‑水‑火‑风‑空各 3 min，无缝串联。 
    - 学习效果：适合多维耗能型学员（身心脑都累），练完能量同步回正，整个人“续满电”。
  - Day 14 回顾日
    - 教授内容：记录最契合自己的元素禅，定制后续组合。
    - 学习效果：帮助找到最适合自己的能量练法，形成“私人日课组合”，后续可随时调用应对各种状态。

- Week 3（四觉禅 & 整合高阶，效果：开启“超觉模式”，把创造力、自愈力、执行力推到个人上限，目标显化更顺）
  - Day 15 音觉禅
    - 教授内容：OM 声波疗愈，激活心轮共振。
    - 学习效果：嗓音弱、表达困难的人练完发声更稳定、圆润，开会、直播 表达自己更有底气和磁性。
  - Day 16 光觉禅
    - 教授内容：顶轮白光观想，扩展光域。
    - 学习效果：记忆力下降、脑沉重、专注力差的人，练完清脑明眼，信息处理与记忆同步提升。
  - Day 17 息觉禅
    - 教授内容：4‑7‑8 深息节奏，心肺同频。
    - 学习效果：心跳快、易疲劳、免疫力差的人练完呼吸心跳同频，自愈力觉醒，身心更有恢复力。
  - Day 18 念觉禅
    - 教授内容：愿景锁定 + 呼吸注入，意念显化。
    - 学习效果：目标模糊、内心迷茫的人，练完意图锁定、方向明确，内在行动感自然形成。
  - Day 19 全息疗愈
    - 教授内容：平躺音频引导，深层身心修复。
    - 学习效果：对未来不安、临场容易慌的人，通过脑内彩排未来关键场景，提升临场底气与自信。
  - Day 20 梦行瑜伽士
    - 教授内容：吐息延长 + 场景预演，未来彩排。
    - 学习效果：先在脑海“彩排”关键场景，上场时底气十足。
  - Day 21 狮吼 + HA 呼吸
    - 教授内容：低沉狮吼 6‑8 声 + 9 轮 HA，阳气冲顶。
    - 学习效果：气弱、阳气不足、社交怕出场的人，一练气场拉满、底层能量冲顶，整个人更亮更强。

# 当前回复策略
${strategy}

# 注意事项
**禁止**提及任何在【系统班知识】中未出现的要素

# 任务
你基于回复策略，从**系统班知识**中识别出3条需要使用的系统班知识

# 输出策略
请严格按照如下 JSON 格式输出:
{{
think:''深度思考的内容,
knowledge:[
  {
    "week": "",
    "day": "",
    "content": "",
    "effect": ""
   }
  ]
}}`

    const jsonRes = await LLM.predict(prompt, { responseJSON: true, meta: { round_id: roundId } })
    const res = JSON.parse(jsonRes)

    const knowledge = res.knowledge.map((item) => {
      return `${item.week} ${item.day} 教授内容：${item.content}学习效果：${item.effect}`
    }).join('\n')

    return knowledge
  }

}