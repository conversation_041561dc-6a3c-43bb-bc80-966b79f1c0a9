import { ChatStateStore } from '../../../storage/chat_state_store'
import { SalesNodeHelper } from '../../flow/helper/salesNodeHelper'
import { MessageSender } from '../../message/message_send'
import { IWecomMsgType } from '../../../../../lib/juzi/type'
import { getUserId } from '../../../../../config/chat_id'
import { SalesPrompt } from '../../../prompt/moer/salesPrompt'
import { EventTracker, IEventType } from '../../../../../model/logger/data_driven'
import { MoreImageGeneral } from '../../rag/moer_image_general'
import { sleep } from '../../../../../lib/schedule/schedule'
import { DataService } from '../../../getter/getData'

export interface IActionInfo {
    additionInfo: string;
    callback?: () => Promise<void>;
}

export class PostAction {

  public static async sendInvitation(chat_id: string): Promise<IActionInfo> {
    const user_id = getUserId(chat_id)
    const actionInfo: IActionInfo = {
      additionInfo: '\n务必向客户发送下单链接https://t.meihao.com/HhYJ，并邀约客户购买',
    }

    actionInfo.callback = async () => {
      await PostAction.sendInstallmentVideo(chat_id, user_id)
    }


    return actionInfo
  }

  public static async sendCaseImage(chat_id: string, round_id:  string): Promise<IActionInfo> {
    const user_id = getUserId(chat_id)
    const imageStr = await SalesPrompt.getSalesCase(chat_id, round_id)
    if (!imageStr) {
      return { additionInfo: '' }
    }
    EventTracker.track(chat_id, IEventType.SalesCase, { round_id: round_id, case: imageStr })

    // 最多发送2张图片
    const images = imageStr.split('，').slice(0, 2)

    const imageSearchResult = await Promise.all(images.map(async (image) => {
      const resultList = await MoreImageGeneral.searchSalesCaseImage(image, ['sales_case'], 1)
      return resultList[0] ?? { description: '', url: '' }
    }))

    const imagesShouldSend = imageSearchResult.filter(async (image) => image.url !== '' && !await SalesNodeHelper.isRepeatedMsg(chat_id, image.description, 20))

    if (imagesShouldSend.length > 0) {
      //发送图片函数
      const sendImages = async () => {
        for (const image of imagesShouldSend) {
          await sleep(2000)
          await MessageSender.sendById({
            user_id: user_id,
            chat_id: chat_id,
            ai_msg: image.description,
            send_msg:{
              type: IWecomMsgType.Image,
              url: image.url
            }
          })
        }
      }

      const additionInfo = `\n案例图片信息：
${imagesShouldSend.map((image) => image.description).join('\n')}`
      return {
        additionInfo: additionInfo,
        callback: sendImages
      }
    }

    return { additionInfo: '' }
  }

  public static async sendPreCourseLink(chat_id: string): Promise<IActionInfo> {
    const preCourseLink = await DataService.getCourseLink(0, chat_id)

    const actionInfo: IActionInfo = {
      additionInfo: `
小讲堂链接：${preCourseLink}`,
    }

    return actionInfo
  }

  private static async sendInstallmentVideo(chat_id: string, user_id: string) {
    if (ChatStateStore.get(chat_id).state.is_send_installment_video) {
      return
    }

    const chatHistory = await SalesNodeHelper.getChatHistory(chat_id, 5, 20)
    const hasInstallmentMessage = chatHistory.includes('分期')
    if (!hasInstallmentMessage) {
      return
    }

    ChatStateStore.update(chat_id, {
      state: {
        is_send_installment_video: true
      }
    })

    // 发送分期付款视频
    const installmentMessage = '如果您需要分期付款，班班给您发个分期操作视频哈'
    await MessageSender.sendById({
      user_id: user_id,
      chat_id: chat_id,
      ai_msg: installmentMessage
    })
    await MessageSender.sendById({
      user_id: user_id,
      chat_id: chat_id,
      ai_msg: '[分期操作指导视频]',
      send_msg: {
        type:IWecomMsgType.Video,
        url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%e5%88%86%e6%9c%9f%e6%96%b9%e6%a1%88%e8%a7%86%e9%a2%91.mp4'
      }
    })
  }
}