import { MetaActionComponent } from './meta_action_component'
import { IActionInfo } from './stages/post_action'
import { Introduction } from './stages/introduction'
import { AfterIntroduction } from './stages/after_introduction'
import { DuringCourse } from './stages/during_course'
import { AfterCourse1 } from './stages/after_course1'
import { AfterCourse3Sales } from './stages/after_course3_sales'
import { AfterCourse4 } from './stages/after_course4'
import { AfterCourseWeek } from './stages/after_courseweek'
import { AfterPaid } from './stages/after_paid'
import logger from '../../../../model/logger/logger'

export interface MetaActionStage {
    thinkPrompt: string
    metaActions: string
}

export class MetaActionRouter {
  public static componentList: MetaActionComponent[] = []
  public static actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {} as Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>>

  /**
     * 初始化使用的元行为组件
     * 注意：组件调度优先级由组件的添加顺序决定
     */
  public static async initMetaAction(chat_id: string, round_id: string) {
    this.componentList.push(new AfterPaid())
    this.componentList.push(new DuringCourse())
    this.componentList.push(new AfterCourseWeek())
    this.componentList.push(new AfterCourse4())
    this.componentList.push(new AfterCourse3Sales())
    this.componentList.push(new AfterCourse1())
    this.componentList.push(new AfterIntroduction())
    this.componentList.push(new Introduction())

    await this.registerActionList(chat_id, round_id)
  }

  private static async registerActionList(chat_id: string, round_id: string) {
    for (const component of this.componentList) {
      const actionList = await component.getAction(chat_id, round_id)
      if (actionList) {
        this.actionMap  = { ...this.actionMap, ...actionList }
      }
    }
  }

  public static async handleAction(chat_id: string, round_id: string, actions: string[]): Promise<IActionInfo> {

    if (this.componentList.length === 0) {
      await this.initMetaAction(chat_id,  round_id)
    }

    const actionInfo:IActionInfo = {
      additionInfo: '',
    }
    if (!this.actionMap || Object.keys(this.actionMap).length == 0) {
      return actionInfo
    }

    for (const action of actions) {
      if (this.actionMap[action]) {
        return await this.actionMap[action](chat_id, round_id)
      }
    }

    return actionInfo
  }

  public static async getThinkAndMetaActions(chat_id: string, round_id: string) : Promise<MetaActionStage> {
    if (this.componentList.length === 0) {
      await this.initMetaAction(chat_id,  round_id)
    }

    const activeComponent = await this.getActiveStage(chat_id)

    if (!activeComponent) {
      logger.error({ chat_id:chat_id }, '没有可用的元行为组')
      return {
        thinkPrompt: '',
        metaActions: '',
      }
    }

    logger.trace({ chat_id: chat_id }, `当前使用的元行为组：${activeComponent.constructor.name}`)

    const thinkPrompt = await activeComponent.getThinkPrompt(chat_id)
    const metaAction = await activeComponent.getMetaAction(chat_id)

    return {
      thinkPrompt,
      metaActions: this.formatMetaActionPrompt(metaAction),
    }
  }

  public static async getGuidance(chat_id: string, strategy: string, actions: string[], round_id: string) {
    if (this.componentList.length === 0) {
      await this.initMetaAction(chat_id, round_id)
    }
    const activeComponent = await this.getActiveStage(chat_id)
    if (!activeComponent) {
      return ''
    }
    return await activeComponent.getGuidance(chat_id, strategy, actions, round_id)
  }

  private static async getActiveStage(chat_id: string) {
    for (const component of this.componentList) {
      if (await component.isStageActive(chat_id)) {
        return component
      }
    }
    return null
  }

  static formatMetaActionPrompt(metaAction:Record<string, string>): string {
    return Object.entries(metaAction).map(([key, value]) => `- ${key}：${value}`).join('\n')
  }
}