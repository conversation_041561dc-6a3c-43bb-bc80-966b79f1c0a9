import { IActionInfo } from './stages/post_action'

/**
 * 元行为组
 */
export abstract class MetaActionComponent {
  /**
   * 激活元行为组的条件
   * 注意，重复命名的action，调度逻辑会被后者覆盖
   */
  public abstract isStageActive(chatId: string): Promise<boolean>;
  /**
   * 使用哪一组Meta action
   */
  public abstract getMetaAction(chatId: string): Promise<Record<string, string>>;

  /**
   * 使用什么thinkPrompt
   */
  public abstract getThinkPrompt(chatId: string): Promise<string>;

  /**
   * 能使用什么action
   */
  public abstract getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null>

  /**
   * 元行为组激活后的stagePrompt
   * @param chatId
   */
  public abstract getBaseGuidance(chatId: string): Promise<string>;

  /**
   * 根据think结果动态获取信息
   * @param chatId
   * @param strategy
   * @param actions
   * @param roundId
   */
  public abstract getGuidance(chatId: string, strategy: string, actions:string[], roundId: string): Promise<string>;

  /**
   * 使用元行为组前的处理逻辑
   * @param chatId
   * @param roundId
   */
  public abstract prepareActivation(chatId: string, roundId: string): Promise<void>;


}
