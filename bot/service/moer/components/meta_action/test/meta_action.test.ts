import { UUID } from '../../../../../lib/uuid/uuid'
import { AfterCourse4 } from '../stages/after_course4'


describe('MetaActionTest', () => {

  it('testExtractSystemCourseKnowledge', async () => {
    const strategy = '聚焦于客户“觉知觉醒、减少浮躁”的核心目标，将系统班中提升觉察力、情绪掌控等具体模块与客户诉求一一对应，让客户看到课程如何帮他实现理想状态。同时指出，如果仅停留于入门营体验且未形成持续练习，往往容易陷入反复浮躁、难以保持清醒自律，引导其认同深度学习的重要性，为报名做心理铺垫。'
    const roundId = UUID.v4()
    // const knowledge = await AfterCourse4.extractSystemCourseKnowledge(strategy, roundId)
    // console.log(knowledge)
  }, 9e8)
})