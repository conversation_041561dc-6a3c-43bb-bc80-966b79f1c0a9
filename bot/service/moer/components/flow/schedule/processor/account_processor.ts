import { Job } from 'bullmq'
import { TaskName } from '../type'
import { MoerAPI } from '../../../../../../model/moer_api/moer'
import { DataService } from '../../../../getter/getData'
import RateLimiter from '../../../../../../model/redis/rate_limiter'
import { Config } from '../../../../../../config/config'
import logger from '../../../../../../model/logger/logger'
import { JuziAPI } from '../../../../../../lib/juzi/api'
import { GroupNotification } from '../../../../notification/group'
import { EventTracker, IEventType } from '../../../../../../model/logger/data_driven'
import { AsyncLock } from '../../../../../../lib/lock/lock'
import { sleep } from '../../../../../../lib/schedule/schedule'
import { YiwiseAPI } from '../../../../../../lib/yiwise/yiwise_api'
import { PhoneBindTask } from '../task/phoneBind'

interface IRumengyingOrderEvent {
    logid: string // 日志 ID
    event: string // 事件类型
    userId: number // 客户 ID
    mobile: string // 手机号
    goodsName: string // 商品名称
    sku: string // 商品 SKU
    stage: number // 阶段
    simpleName: string // 老师名称
    simpleId: number // 老师 ID
    transferWay: string //渠道类型

    retryTime: number // 第几次重试
}


export class AccountEventProcessor {
  public static async handle(job: Job) {
    const data =  job.data

    switch (job.name) {
      case TaskName.RumenyingOrder:
        this.handleRumenyingOrder(data)
        break
      case TaskName.AddPhoneBindTask:
        await PhoneBindTask.addBindPhoneTask(data)
        break
      case TaskName.PhoneBindCheck:
        await new PhoneBindTask().phoneBindCheck(data)
    }
  }

  private static async handleRumenyingOrder(data: any) {
    const event = data as IRumengyingOrderEvent
    const moerId = event.userId.toString(10)

    // 如果成功加微
    if (await MoerAPI.isBindWechat(moerId)) {
      return
    }

    // 数据库里面再查一下，兜底
    if (await DataService.getChatIdByMoerId(moerId)) {
      return
    }

    // 5min 自动加微流程
    if (event.retryTime === 1) {
      // 调用企微 API 添加， 一天不能超过 20 次
      const  apiAddWechatLimiter =  new RateLimiter({
        windowSize: 24 * 60 * 60, // 24 hours
        maxRequests: 20,
      })

      if (!await apiAddWechatLimiter.isAllowed('api_add_wecom', Config.setting.wechatConfig?.id as string)) {
        logger.warn('企微API 调用次数超过当天限流')
        return
      }

      // 加锁，1 分钟 1个账号 只能添加一次
      const lock = new AsyncLock()

      await lock.acquire(Config.setting.wechatConfig?.id as string, async () => {
        EventTracker.track(event.userId.toString(10), IEventType.AddWechatAPI, { ...event, addWechatType: 'API加微' })

        await JuziAPI.addFriendByPhone({
          imBotId: Config.setting.wechatConfig?.id as string,
          phone: event.mobile,
          hello: '我是您购买的冥想入门营的助教老师'
        })

        await sleep(10 * 1000) // 等 10s 加下个号
      }, {
        timeout: 60 * 1000
      })

    } else if (event.retryTime === 2) {
      //电话加微 10min
      EventTracker.track(event.userId.toString(10), IEventType.AddWechatPhoneCall, { ...event, addWechatType: '电话加微Day1-5min' })
      await YiwiseAPI.addFriendPhoneCallNotify(YiwiseAPI.PhoneCallJobIdDay1_5min, event.mobile, event.simpleName, this.getTransferWayDescription(event.transferWay))
    } else if (event.retryTime === 3) {
      EventTracker.track(event.userId.toString(10), IEventType.AddWechatManually, { ...event, addWechatType: '通知人工加微' })
      //通知人工加微40min
      await GroupNotification.notify(`账号 ${Config.setting.wechatConfig?.name} ${event.mobile} 40分钟 仍未添加企微助教，请人工处理`)
    } else if (event.retryTime === 4) {
      //一天后电话加微
      EventTracker.track(event.userId.toString(10), IEventType.AddWechatPhoneCall, { ...event, addWechatType: '电话加微Day2' })
      await YiwiseAPI.addFriendPhoneCallNotify(YiwiseAPI.PhoneCallJobIdDay2, event.mobile, event.simpleName, this.getTransferWayDescription(event.transferWay))
    } else if (event.retryTime === 5) {
      //周日统一电话加微
      EventTracker.track(event.userId.toString(10), IEventType.AddWechatPhoneCall, { ...event, addWechatType: '电话加微周日' })
      await YiwiseAPI.addFriendPhoneCallNotify(YiwiseAPI.PhoneCallSundayJob, event.mobile, event.simpleName, this.getTransferWayDescription(event.transferWay))
    } else if (event.retryTime === 30) {
      //电话加微 30min
      EventTracker.track(event.userId.toString(10), IEventType.AddWechatPhoneCall, { ...event, addWechatType: '电话加微Day1-30min' })
      await YiwiseAPI.addFriendPhoneCallNotify(YiwiseAPI.PhoneCallJobIdDay1_30min, event.mobile, event.simpleName, this.getTransferWayDescription(event.transferWay))
    }
  }

  private static getTransferWayDescription(way: string) {
    const transferWayMap = new Map<string, string>([
      ['1', '微信'],
      ['2', '支付宝'],
      ['3', '银联快捷支付'],
      ['4', '墨贝支付'],
      ['5', '苹果支付'],
      ['6', '墨读会预约'],
      ['7', '直播预约'],
      ['8', '领取限时免费'],
      ['9', '线下付款'],
      ['10', '微信公众号支付'],
      ['11', '分享家支付'],
      ['12', '兑换码赠送,不记录订单表,只是占用这个12的数'],
      ['13', '助力成功赠送,不记录订单表,只是只能用这个13'],
      ['14', '抖音支付'],
      ['15', '飞鱼支付'],
      ['16', '喜马拉雅支付'],
      ['17', '抖音'],
      ['18', '微信视频号'],
      ['19', '快手小店订单'],
      ['20', '百度订单'],
      ['21', '快手'],
      ['22', '落地页渠道商'],
      ['23', '买赠活动赠送'],
      ['24', '小红书'],
      ['25', '海豚'],
      ['26', '微信'],
      ['27', '支付宝周期购'],
      ['28', '苹果自动扣款'],
      ['29', '百度'],
      ['30', '支付宝h5支付'],
      ['31', '微信支付h5'],
      ['32', 'uc'],
      ['33', '知乎'],
      ['34', '抖音团购兑换'],
      ['35', '领客平台订单'],
      ['36', '转增订单'],
      ['37', '小红书']
    ])
    return transferWayMap.get(way) || '线上购买'
  }

}