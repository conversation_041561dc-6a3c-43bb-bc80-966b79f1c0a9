import { BaseTask, trackProcess } from './baseTask'
import { ClassTaskName } from '../type'
import { ITask } from '../../../schedule/type'
import { Config } from '../../../../../../config/config'
import { DataService } from '../../../../getter/getData'
import { ClassGroupPreCourseSundayScript } from '../../../script/pre_course_day_class_group_script'
import { ClassGroupFirstCourseDayScript } from '../../../script/course_day1_group_script'
import { ClassGroupSecondCourseDayScript } from '../../../script/course_day2_group_script'
import { ClassGroupThirdCourseDayScript } from '../../../script/course_day3_group_script'
import { ClassGroupForthCourseDayScript } from '../../../script/course_day4_group_script'
import { ClassGroupFifthCourseDayScript } from '../../../script/course_day5_group_script'
import logger from '../../../../../../model/logger/logger'
import { MessageSender } from '../../../message/message_send'
import { IScheduleTime } from '../../../schedule/creat_schedule_task'
import { JuziAPI } from '../../../../../../lib/juzi/api'
import { catchError } from '../../../../../../lib/error/catchError'
import { sleep } from '../../../../../../lib/schedule/schedule'

export class ClassGroupSend extends BaseTask {
  public static async getTask() {
    const tasks: ITask[] = []
    const baseInfo = {
      chatId: `${Config.setting.wechatConfig?.id}_${Config.setting.wechatConfig?.classGroupId.replaceAll(':', '')}`,
      userId: Config.setting.wechatConfig?.classGroupId as string,
    }

    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassGroupBuildNotify,
      scheduleTime: {
        is_course_week: false,
        day: 5,
        time: '21:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: ClassTaskName.GoodMorningIntroduction,
      scheduleTime: {
        is_course_week: false,
        day: 6,
        time: '08:30:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.PreClassReminder,
      scheduleTime: {
        is_course_week: false,
        day: 6,
        time: '19:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassGroupBuildNotify1,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '09:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassGroupBuildNotify2,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '11:30:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassGroupBuildNotify3,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '15:12:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '15:12:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.PreCourseLinkSend,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '17:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.OpenCeremonyArrangement,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '18:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.GroupAnnouncement,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '18:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.OpenCeremonyNotify1,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '19:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.OpenCeremonyNotify2,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '19:55:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '19:55:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.OpenCeremony,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '20:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '20:20:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: ClassTaskName.MorningGreeting1Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '06:40:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '07:10:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.GroupAnnouncement,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '07:10:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.MorningGreeting2Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '10:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.NoonGreeting1Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '14:30:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.NoonGreeting2Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '16:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.NoonGreeting3Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '17:35:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify1Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '18:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify2Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '19:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.GroupAnnouncement,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '19:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify3Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '19:48:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify4Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '19:50:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '19:58:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting1Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting2Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:05:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting3_1Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:18:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting3_2Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:21:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting3_3Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:24:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting4Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:35:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting5Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:39:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting6Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:45:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting7Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:59:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting8Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '21:12:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting9Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '21:26:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting10Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '21:30:30'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '21:43:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.AfterClassMeeting1Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '21:45:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.GroupAnnouncement,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '22:10:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.AfterClassMeeting2Day1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '22:15:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: ClassTaskName.MorningGreeting1Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '06:12:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.MorningGreeting2Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '10:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.NoonGreeting1Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '14:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.NoonGreeting2Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '16:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.NoonGreeting3Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '17:10:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify1Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '18:10:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify2Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '19:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.GroupAnnouncement,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '19:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify3Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '19:45:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '19:58:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting1Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting1_2Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:04:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting2Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:13:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting3Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:20:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting4Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:29:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting5Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:35:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting6Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:54:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting7Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '21:07:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '21:13:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.AfterClassMeeting1Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '21:15:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.AfterClassMeeting2Day2,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '22:12:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.GroupAnnouncement,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '22:12:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: ClassTaskName.EarlyMorningGreetingDay3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '06:30:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.MorningGreetingDay3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '10:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.NoonGreetingDay3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '16:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '16:15:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.AfternoonGreetingDay3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '17:20:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify1Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '17:55:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify2Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '19:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.GroupAnnouncement,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '19:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '19:58:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify3Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting1Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:06:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting2Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:12:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting3Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:19:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting4_1Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:30:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting4_2Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:35:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting5Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:39:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting6Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:50:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting7Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '21:10:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting8Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '21:28:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting9Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '21:46:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting10Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '21:51:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.AfterClassMeeting1Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '21:58:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.AfterClassMeeting2Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '22:09:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.AfterClassMeeting3Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '22:21:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.AfterClassMeeting4Day3,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '23:15:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: ClassTaskName.MorningGreetingDay4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '07:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.MorningGreeting1Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '10:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.NoonGreeting1Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '14:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.GroupAnnouncement,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '14:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '14:04:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.NoonGreeting2Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '16:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify1Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '18:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify2Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '19:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify3Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting1Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:02:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting2Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:13:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting3Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:23:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting4Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:31:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting5Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:37:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting6Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:49:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting7Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:55:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting8Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '21:03:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting9Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '21:17:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting10Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '21:32:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting11Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '21:43:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting12Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '21:54:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting13Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '22:07:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeeting14Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '22:19:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '22:24:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.AfterClassMeeting1Day4,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '22:26:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: ClassTaskName.MorningGreeting1Day5,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '09:26:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.MorningGreeting2Day5,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '10:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.NoonGreetingDay5,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '14:10:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify1Day5,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '16:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify2Day5,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '16:56:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingNotify3Day5,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '18:10:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ClassMeetingDay5,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '20:00:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '20:15:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.RemoveFromGroup,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '20:18:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.ModifyGroupName,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '20:40:00'
      }
    })
    tasks.push({
      ...baseInfo,
      name: ClassTaskName.GroupAnnouncement,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '20:40:00'
      }
    })
    return tasks
  }

  @trackProcess
  public async process(task: ITask) {
    if (Config.setting.localTest || Config.setting.wechatConfig?.id === '1688854546332791') {
      if (task.scheduleTime) {
        await MessageSender.sendById({
          room_id: task.userId,
          chat_id: task.chatId,
          ai_msg: `${task.name} ${task.scheduleTime.is_course_week ? '上课周' : '非上课周'} ， 星期${task.scheduleTime.day} ${task.scheduleTime.time}`,
        }, {
          notAddBotMessage: true
        })
      }
    }

    const currentTime = await DataService.getCurrentTime(task.chatId)
    const taskTime = task.scheduleTime as IScheduleTime
    const bot_id = Config.setting.wechatConfig?.id as string
    const botUserId = Config.setting.wechatConfig?.botUserId as string
    let courseNo = await DataService.getCourseNoByChatId(task.chatId)
    if (!courseNo) {
      logger.warn({ message: `课程号未找到 ${task.chatId}` })
      courseNo =  DataService.getNextWeekCourseNo()
    }

    const [
      preCourseLink,
      liveLink,
    ] = await Promise.all([
      DataService.getCourseLinkByCourseNo(courseNo, 0),
      DataService.getCourseLink(currentTime.day, task.chatId),
    ])

    const courseDate = await DataService.getCourseStartTime(task.chatId)
    const courseDateString = courseDate.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })

    let messages

    switch (task.name) {
      case ClassTaskName.ClassGroupBuildNotify: {

        messages = ClassGroupPreCourseSundayScript.class_group_build_notify(preCourseLink, courseDateString)
        break
      }
      case ClassTaskName.GoodMorningIntroduction: {
        messages = ClassGroupPreCourseSundayScript.good_morning_introduction(preCourseLink, courseDateString)
        break
      }
      case ClassTaskName.PreClassReminder: {
        messages = ClassGroupPreCourseSundayScript.pre_class_reminder(preCourseLink, courseDateString)
        break
      }
      case ClassTaskName.ClassGroupBuildNotify1: {
        messages = ClassGroupPreCourseSundayScript.class_group_build_notify1(preCourseLink)
        break
      }
      case ClassTaskName.ClassGroupBuildNotify2: {
        messages = ClassGroupPreCourseSundayScript.class_group_build_notify2(preCourseLink)
        break
      }
      case ClassTaskName.ClassGroupBuildNotify3: {
        messages = ClassGroupPreCourseSundayScript.class_group_build_notify3(preCourseLink)
        break
      }
      case ClassTaskName.PreCourseLinkSend: {
        messages = ClassGroupPreCourseSundayScript.pre_course_link_send(preCourseLink, courseDateString)
        break
      }
      case ClassTaskName.OpenCeremonyArrangement: {
        messages = ClassGroupPreCourseSundayScript.open_ceremony_arrangement(preCourseLink, courseDateString)
        break
      }
      case ClassTaskName.OpenCeremonyNotify1: {
        messages = ClassGroupPreCourseSundayScript.open_ceremony_notify1()
        break
      }
      case ClassTaskName.OpenCeremony: {
        messages = ClassGroupPreCourseSundayScript.open_ceremony(preCourseLink, courseDateString)
        break
      }
      case ClassTaskName.MorningGreeting1Day1: {
        messages = ClassGroupFirstCourseDayScript.morning_greeting1_day1(preCourseLink, courseDateString)
        break
      }
      case ClassTaskName.MorningGreeting2Day1: {
        messages = ClassGroupFirstCourseDayScript.morning_greeting2_day1(preCourseLink, courseDateString)
        break
      }
      case ClassTaskName.NoonGreeting1Day1: {
        messages = ClassGroupFirstCourseDayScript.noon_greeting1_day1(preCourseLink, courseDateString)
        break
      }
      case ClassTaskName.NoonGreeting2Day1: {
        messages = ClassGroupFirstCourseDayScript.noon_greeting2_day1(preCourseLink)
        break
      }
      case ClassTaskName.NoonGreeting3Day1: {
        messages = ClassGroupFirstCourseDayScript.noon_greeting3_day1(preCourseLink, courseDateString)
        break
      }
      case ClassTaskName.ClassMeetingNotify1Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting_notify1_day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeetingNotify2Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting_notify2_day1()
        break
      }
      case ClassTaskName.ClassMeetingNotify3Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting_notify3_day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeetingNotify4Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting_notify4_day1()
        break
      }
      case ClassTaskName.ClassMeeting1Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting1_day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting2Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting2_day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting3_1Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting3_1day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting3_2Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting3_2day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting3_3Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting3_3day1()
        break
      }
      case ClassTaskName.ClassMeeting4Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting4_day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting5Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting5_day1()
        break
      }
      case ClassTaskName.ClassMeeting6Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting6_day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting7Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting7_day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting8Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting8_day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting9Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting9_day1(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting10Day1: {
        messages = ClassGroupFirstCourseDayScript.class_meeting10_day1(liveLink)
        break
      }
      case ClassTaskName.AfterClassMeeting1Day1: {
        messages = ClassGroupFirstCourseDayScript.after_class_meeting1_day1()
        break
      }
      case ClassTaskName.AfterClassMeeting2Day1: {
        messages = ClassGroupFirstCourseDayScript.after_class_meeting2_day1()
        break
      }
      case ClassTaskName.MorningGreeting1Day2: {
        const recordingLinkDay2 = await DataService.getCourseLink(1, task.chatId, true)
        messages = ClassGroupSecondCourseDayScript.morning_greeting1_day2(recordingLinkDay2)
        break
      }
      case ClassTaskName.MorningGreeting2Day2: {
        const recordingLinkDay2 = await DataService.getCourseLink(1, task.chatId, true)
        messages = ClassGroupSecondCourseDayScript.morning_greeting2_day2(recordingLinkDay2)
        break
      }
      case ClassTaskName.NoonGreeting1Day2: {
        const recordingLinkDay2 = await DataService.getCourseLink(1, task.chatId, true)
        messages = ClassGroupSecondCourseDayScript.noon_greeting1_day2(recordingLinkDay2)
        break
      }
      case ClassTaskName.NoonGreeting2Day2: {
        const recordingLinkDay2 = await DataService.getCourseLink(1, task.chatId, true)
        messages = ClassGroupSecondCourseDayScript.noon_greeting2_day2(recordingLinkDay2)
        break
      }
      case ClassTaskName.NoonGreeting3Day2: {
        messages = ClassGroupSecondCourseDayScript.noon_greeting3_day2()
        break
      }
      case ClassTaskName.ClassMeetingNotify1Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting_notify1_day2(liveLink)
        break
      }
      case ClassTaskName.ClassMeetingNotify2Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting_notify2_day2()
        break
      }
      case ClassTaskName.ClassMeetingNotify3Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting_notify3_day2()
        break
      }
      case ClassTaskName.ClassMeeting1Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting1_1day2(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting1_2Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting1_2day2()
        break
      }
      case ClassTaskName.ClassMeeting2Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting2_day2()
        break
      }
      case ClassTaskName.ClassMeeting3Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting3_day2(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting4Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting4_day2(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting5Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting5_day2(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting6Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting6_day2(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting7Day2: {
        messages = ClassGroupSecondCourseDayScript.class_meeting7_day2(liveLink)
        break
      }
      case ClassTaskName.AfterClassMeeting1Day2: {
        messages = ClassGroupSecondCourseDayScript.after_class_meeting1_day2()
        break
      }
      case ClassTaskName.AfterClassMeeting2Day2: {
        const recordingLinkDay2 = await DataService.getCourseLink(1, task.chatId, true)
        messages = ClassGroupSecondCourseDayScript.after_class_meeting2_day2(recordingLinkDay2)
        break
      }
      case ClassTaskName.EarlyMorningGreetingDay3: {
        const recordingLinkDay3 = await DataService.getCourseLink(2, task.chatId, true)
        messages = ClassGroupThirdCourseDayScript.early_morning_greeting_day3(recordingLinkDay3)
        break
      }
      case ClassTaskName.MorningGreetingDay3: {
        const recordingLinkDay3 = await DataService.getCourseLink(2, task.chatId, true)
        messages = ClassGroupThirdCourseDayScript.morning_greeting_day3(recordingLinkDay3)
        break
      }
      case ClassTaskName.NoonGreetingDay3: {
        const recordingLinkDay3 = await DataService.getCourseLink(2, task.chatId, true)
        messages = ClassGroupThirdCourseDayScript.noon_greeting_day3(recordingLinkDay3)
        break
      }
      case ClassTaskName.AfternoonGreetingDay3: {
        const recordingLinkDay3 = await DataService.getCourseLink(2, task.chatId, true)
        messages = ClassGroupThirdCourseDayScript.afternoon_greeting_day3(recordingLinkDay3)
        break
      }
      case ClassTaskName.ClassMeetingNotify1Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting_notify1_day3()
        break
      }
      case ClassTaskName.ClassMeetingNotify2Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting_notify2_day3(liveLink)
        break
      }
      case ClassTaskName.ClassMeetingNotify3Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting_notify3_day3(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting1Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting1_day3()
        break
      }
      case ClassTaskName.ClassMeeting2Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting2_day3(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting3Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting3_day3(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting4_1Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting4_1day3(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting4_2Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting4_2day3(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting5Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting5_day3(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting6Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting6_day3(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting7Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting7_day3(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting8Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting8_day3(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting9Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting9_day3()
        break
      }
      case ClassTaskName.ClassMeeting10Day3: {
        messages = ClassGroupThirdCourseDayScript.class_meeting10_day3(liveLink)
        break
      }
      case ClassTaskName.AfterClassMeeting1Day3: {
        messages = ClassGroupThirdCourseDayScript.after_class_meeting1_day3()
        break
      }
      case ClassTaskName.AfterClassMeeting2Day3: {
        messages = ClassGroupThirdCourseDayScript.after_class_meeting2_day3()
        break
      }
      case ClassTaskName.AfterClassMeeting3Day3: {
        messages = ClassGroupThirdCourseDayScript.after_class_meeting3_day3()
        break
      }
      case ClassTaskName.AfterClassMeeting4Day3: {
        const recordingLinkDay3 = await DataService.getCourseLink(3, task.chatId, true)
        messages = ClassGroupThirdCourseDayScript.after_class_meeting4_day3(recordingLinkDay3)
        break
      }
      case ClassTaskName.MorningGreetingDay4: {
        const recordingLinkDay4 = await DataService.getCourseLink(3, task.chatId, true)
        messages = ClassGroupForthCourseDayScript.morning_greeting_day4(recordingLinkDay4)
        break
      }
      case ClassTaskName.MorningGreeting1Day4: {
        const recordingLinkDay4 = await DataService.getCourseLink(3, task.chatId, true)
        messages = ClassGroupForthCourseDayScript.morning_greeting1_day4(recordingLinkDay4)
        break
      }
      case ClassTaskName.NoonGreeting1Day4: {
        messages = ClassGroupForthCourseDayScript.noon_greeting_day4()
        break
      }
      case ClassTaskName.NoonGreeting2Day4:{
        messages = ClassGroupForthCourseDayScript.noon_greeting_day4_1()
        break
      }
      case ClassTaskName.ClassMeetingNotify1Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting_notify1_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeetingNotify2Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting_notify2_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeetingNotify3Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting_notify3_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting1Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting1_day4()
        break
      }
      case ClassTaskName.ClassMeeting2Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting2_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting3Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting3_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting4Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting4_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting5Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting5_day4()
        break
      }
      case ClassTaskName.ClassMeeting6Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting6_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting7Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting7_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting8Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting8_day4()
        break
      }
      case ClassTaskName.ClassMeeting9Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting9_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting10Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting10_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting11Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting11_day4()
        break
      }
      case ClassTaskName.ClassMeeting12Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting12_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting13Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting13_day4(liveLink)
        break
      }
      case ClassTaskName.ClassMeeting14Day4: {
        messages = ClassGroupForthCourseDayScript.class_meeting14_day4()
        break
      }
      case ClassTaskName.AfterClassMeeting1Day4: {
        messages = ClassGroupForthCourseDayScript.after_class_meeting1_day4()
        break
      }
      case ClassTaskName.MorningGreeting1Day5: {
        const recordingLinkDay5 = await DataService.getCourseLink(4, task.chatId, true)
        messages = ClassGroupFifthCourseDayScript.morning_greeting1_day5(recordingLinkDay5)
        break
      }
      case ClassTaskName.MorningGreeting2Day5: {
        const recordingLinkDay5 = await DataService.getCourseLink(4, task.chatId, true)
        messages = ClassGroupFifthCourseDayScript.morning_greeting2_day5(recordingLinkDay5)
        break
      }
      case ClassTaskName.NoonGreetingDay5: {
        messages = ClassGroupFifthCourseDayScript.noon_greeting_day5()
        break
      }
      case ClassTaskName.ClassMeetingNotify1Day5: {
        const recordingLinkDay5 = await DataService.getCourseLink(4, task.chatId, true)
        messages = ClassGroupFifthCourseDayScript.class_meeting_notify1_day5(recordingLinkDay5)
        break
      }
      case ClassTaskName.ClassMeetingNotify2Day5: {
        messages = ClassGroupFifthCourseDayScript.class_meeting_notify2_day5()
        break
      }
      case ClassTaskName.ClassMeetingNotify3Day5: {
        const recordingLinkDay5 = await DataService.getCourseLink(4, task.chatId, true)
        messages = ClassGroupFifthCourseDayScript.class_meeting_notify3_day5(recordingLinkDay5)
        await JuziAPI.groupSync(bot_id)
        break
      }
      case ClassTaskName.ClassMeetingDay5: {
        messages = ClassGroupFifthCourseDayScript.class_meeting_day5()
        await JuziAPI.groupSync(bot_id)
        break
      }
      case ClassTaskName.ModifyGroupName: {
        switch (taskTime.day) {
          case 7:
            if (taskTime.time === '15:12:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【今晚8点开营仪式：学完小讲堂】5天冥想入门营10班' })
            } else if (taskTime.time === '19:55:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【群内开营仪式进行中（图文形式）】5天冥想入门营10班' })
            } else if (taskTime.time === '20:20:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【明晚8点第一课：情绪减压】5天冥想入门营10班' })
            }
            break
          case 1:
            if (taskTime.time === '07:10:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【今晚8点第一课：情绪减压】5天冥想入门营10班' })
            } else if (taskTime.time === '19:58:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【直播中->第一课：情绪减压】5天冥想入门营10班' })
            } else if (taskTime.time === '21:43:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【练习第一课+感悟分享】5天冥想入门营10班' })
            }
            break
          case 2:
            if (taskTime.time === '19:58:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【直播中->第二课：财富果园】5天冥想入门营10班' })
            } else if (taskTime.time === '21:13:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【练习第二课+财富果园解读】5天冥想入门营10班' })
            }
            break
          case 3:
            if (taskTime.time === '16:15:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【今晚8点：最后一课红靴子】5天冥想入门营10班' })
            } else if (taskTime.time === '19:58:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【直播中->最后一课：红靴子】5天冥想入门营10班' })
            }
            break
          case 4:
            if (taskTime.time === '14:04:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【今晚八点！惊喜加播：成事显化】5天冥想入门营10班' })
            } else if (taskTime.time === '22:24:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【系统班优惠报名倒计时】5天冥想入门营10班' })
            }
            break
          case 5:
            if (taskTime.time === '20:15:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【我们毕业了】5天冥想入门营10班' })
              await JuziAPI.groupSync(bot_id)
            } else if (taskTime.time === '20:40:00') {
              await JuziAPI.changeRoomName({ imRoomId: task.userId, imBotId: bot_id, name: '🧘‍♀️【入群看群公告：完成小讲堂】5天冥想入门营10班' })
            }
            break
        }
        break
      }
      case ClassTaskName.GroupAnnouncement: {
        switch (taskTime.day) {
          case 1:
            if (taskTime.time === '07:10:00') {
              await MessageSender.sendById({ chat_id: task.chatId, room_id: task.userId, ai_msg: ClassGroupFirstCourseDayScript.group_announcement1(preCourseLink) }, { isAnnouncement: true })
            } else if (taskTime.time === '19:00:00') {
              await MessageSender.sendById({ chat_id: task.chatId, room_id: task.userId, ai_msg: ClassGroupFirstCourseDayScript.group_announcement2(liveLink) }, { isAnnouncement: true })
            } else if (taskTime.time === '21:10:00') {
              const recordingLink = await DataService.getCourseLink(1, task.chatId, true)
              await MessageSender.sendById({ chat_id: task.chatId, room_id: task.userId, ai_msg: ClassGroupFirstCourseDayScript.group_announcement3(recordingLink) }, { isAnnouncement: true })
            }
            break
          case 2:
            if (taskTime.time === '19:00:00') {
              await MessageSender.sendById({ chat_id: task.chatId, room_id: task.userId, ai_msg: ClassGroupSecondCourseDayScript.group_announcement1(liveLink) }, { isAnnouncement: true })
            } else if (taskTime.time === '22:12:00') {
              const recordingLink = await DataService.getCourseLink(2, task.chatId, true)
              await MessageSender.sendById({ chat_id: task.chatId, room_id: task.userId, ai_msg: ClassGroupSecondCourseDayScript.group_announcement2(recordingLink) }, { isAnnouncement: true })
            }
            break
          case 3:
            await MessageSender.sendById({ chat_id: task.chatId, room_id: task.userId, ai_msg: ClassGroupThirdCourseDayScript.group_announcement(liveLink) }, { isAnnouncement: true })
            break
          case 4:
            await MessageSender.sendById({ chat_id: task.chatId, room_id: task.userId, ai_msg: ClassGroupForthCourseDayScript.group_announcement(liveLink) }, { isAnnouncement: true })
            break
          case 5:
            await MessageSender.sendById({ chat_id: task.chatId, room_id: task.userId, ai_msg: ClassGroupFifthCourseDayScript.group_announcement() }, { isAnnouncement: true })
            break
          case 7:
            await MessageSender.sendById({ chat_id: task.chatId, room_id: task.userId, ai_msg: ClassGroupPreCourseSundayScript.group_announcement() }, { isAnnouncement: true })
            break
        }
        break
      }
      case ClassTaskName.RemoveFromGroup: {
        await catchError(JuziAPI.groupSync(bot_id))
        await sleep(5 * 1000)

        await JuziAPI.kickNonWhitelistMembers(bot_id, task.userId)
        break
      }

      default:
        break
    }

    if (messages) {
      await this.sendGroupMsg(task.userId, messages)
    }
  }
}