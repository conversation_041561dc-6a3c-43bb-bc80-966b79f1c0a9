export enum TaskName {
    CheckPreCourseCompletion = '检查小讲堂完成情况',
    SendWelcomeMessage = '发送欢迎语',
    SendEnergyTest = '发送能量测评',
    GroupSendTeacherIntroduction = '群发老师介绍',
    GroupSendGroupInvited = '群发邀请进群',
    Tuesday1403 = '周二别放弃',
    Wednesday1435 = '周三问听课',
    GroupSend = '群发消息',
    GroupSendPushGroupInvited = '催进群',
    GroupSendPushEnergyTest = '催做能量测评',
    FirstDayPhoneLoginReminder = '第一天提醒手机号登录',
    PushOpeningCeremony = '推送开学典礼',
    EndOpeningCeremony = '结束开学典礼',
    CompleteEnergyTest = '完成能量测评',
    MorningGreeting = '早安问候',
    ShareChange = '第一天分享改变',
    FirstDayClassReminder1 = '第一次上课提醒',
    FirstDayClassReminder2 = '第二次上课提醒',
    ClassStartNotification = '正式开始上课提醒',
    CheckIsInClass = '是否到课',
    PostClassFeedback = '课后回访',
    SendRecording = '发送回放',
    RemindRecording = '催促回放+第二天预告',
    SecondDayVideoChannel = '周二老师视频',
    SecondDayClassReminder = '周二上课提醒',
    SecondDayClassReminder1 = '上课提醒1',
    SecondDayClassReminder2 = '上课提醒2',
    SecondClassEndFeedback = '第二天财富果园分享',
    GoodNightMessage = '晚安寄语',
    CourseCompleteAwardAndFeedback = '发布奖励和回访',
    RedShoesSharing = '红鞋子辅垫打包分享',
    ThirdDayClassReminder = '周三催到课',
    ThirdDayClassReminder1 = '第三天第一次上课提醒',
    ThirdDayClassReminder2 = '第三天第二次上课提醒',
    ThirdDayStartNotification = '第三天正式开始',
    SalesPitch = '群发营销',
    FunctionEmphasis = '强调冥想作用',
    ExtraClassResearch = '加播课程调研',
    ExtraClassNotification = '加播课程通知',
    ScheduleExtraClass = '预约加播课',
    LayingOutSummaryYesterday = '铺垫总结昨天说的内容',
    CountdownToClass = '倒计时1小时',
    ThirdClassReminder = '第三课催到课',
    FinalMarketingPitch = '最后营销',
    PaymentAbilityReminder = '付款能力提醒',
    AdditionalMarketing = '追加营销',
    CompletionReward = '完课奖励',
    CampEndReminder = '结营提醒',
    PullDanmu = '拉取弹幕',
    Placeholder = '占位',
    ThirtyDaysReminder = '30天sop任务',
    UnPaidRemind1 = '针对未报名的同学发送1号消息',
    UnPaidRemind2 = '针对未报名的同学发送2号消息',
    PreCourseFinishInteraction = '完课学员互动',
    TPlusNTask = 'T+N任务',

    XiaoHongShuActivationReminder = '小红书激活',
    RumenyingOrder = '入门营订单',
    PhoneBindCheck = '检查手机号绑定情况',
    AddPhoneBindTask = '添加手机号绑定任务',
    UnPaidRemind3 = '针对未报名的同学发送3号消息',
}

export enum ClassTaskName {
    ClassGroupBuildNotify = '建群提醒',
    GoodMorningIntroduction = '周六早安介绍',
    PreClassReminder = '周六课前提醒',
    ClassGroupBuildNotify1 = '建群提醒1',
    ClassGroupBuildNotify2 = '建群提醒2',
    ClassGroupBuildNotify3 = '建群提醒3',
    PreCourseLinkSend = '提醒小讲堂',
    OpenCeremonyArrangement = '班会安排',
    OpenCeremonyNotify1 = '班会提醒1',
    OpenCeremonyNotify2 = '班会前提醒',
    OpenCeremony = '班会',

    MorningGreeting1Day1 = '周一早安分享1',
    MorningGreeting2Day1 = '周一早安分享2',
    NoonGreeting1Day1 = '周一午安分享1',
    NoonGreeting2Day1 = '周一午安分享2',
    NoonGreeting3Day1 = '周一午安分享3',
    ClassMeetingNotify1Day1 = '周一课程提醒1',
    ClassMeetingNotify2Day1 = '周一课程提醒2',
    ClassMeetingNotify3Day1 = '周一课程提醒3',
    ClassMeetingNotify4Day1 = '周一课程提醒4',
    ClassMeeting1Day1 = '周一直播1',
    ClassMeeting2Day1 = '周一直播2',
    ClassMeeting3_1Day1 = '周一直播3_1',
    ClassMeeting3_2Day1 = '周一直播3_2',
    ClassMeeting3_3Day1 = '周一直播3_3',
    ClassMeeting4Day1 = '周一直播4',
    ClassMeeting5Day1 = '周一直播5',
    ClassMeeting6Day1 = '周一直播6',
    ClassMeeting7Day1 = '周一直播7',
    ClassMeeting8Day1 = '周一直播8',
    ClassMeeting9Day1 = '周一直播9',
    ClassMeeting10Day1 = '周一直播10',
    AfterClassMeeting1Day1 = '周一课后分享1',
    AfterClassMeeting2Day1 = '周一课后分享2',

    MorningGreeting1Day2 = '周二早安分享1',
    MorningGreeting2Day2 = '周二早安分享',
    NoonGreeting1Day2 = '周二午安分享1',
    NoonGreeting2Day2 = '周二午安分享2',
    NoonGreeting3Day2 = '周二午安分享3',
    ClassMeetingNotify1Day2 = '周二课程提醒1',
    ClassMeetingNotify2Day2 = '周二课程提醒2',
    ClassMeetingNotify3Day2 = '周二课程提醒3',
    ClassMeeting1Day2 = '周二直播1',
    ClassMeeting1_2Day2 = '周二直播1_2',
    ClassMeeting2Day2 = '周二直播2',
    ClassMeeting3Day2 = '周二直播3',
    ClassMeeting4Day2 = '周二直播4',
    ClassMeeting5Day2 = '周二直播5',
    ClassMeeting6Day2 = '周二直播6',
    ClassMeeting7Day2 = '周二直播7',
    AfterClassMeeting1Day2 = '周二课后分享1',
    AfterClassMeeting2Day2 = '周二课后分享2',

    EarlyMorningGreetingDay3 = '周三凌晨早安分享',
    MorningGreetingDay3 = '周三早安分享',
    NoonGreetingDay3 = '周三午安分享',
    AfternoonGreetingDay3 = '周三下午分享',
    ClassMeetingNotify1Day3 = '周三课程提醒1',
    ClassMeetingNotify2Day3 = '周三课程提醒2',
    ClassMeetingNotify3Day3 = '周三课程提醒3',
    ClassMeeting1Day3 = '周三直播1',
    ClassMeeting2Day3 = '周三直播2',
    ClassMeeting3Day3 = '周三直播3',
    ClassMeeting4_1Day3 = '周三直播4_1',
    ClassMeeting4_2Day3 = '周三直播4_2',
    ClassMeeting5Day3 = '周三直播5',
    ClassMeeting6Day3 = '周三直播6',
    ClassMeeting7Day3 = '周三直播7',
    ClassMeeting8Day3 = '周三直播8',
    ClassMeeting9Day3 = '周三直播9',
    ClassMeeting10Day3 = '周三直播10',
    AfterClassMeeting1Day3 = '周三课后分享1',
    AfterClassMeeting2Day3 = '周三课后分享2',
    AfterClassMeeting3Day3 = '周三课后分享3',
    AfterClassMeeting4Day3 = '周三课后分享4',

    MorningGreetingDay4 = '周四早安分享',
    NoonGreeting1Day4 = '周四午安分享',
    NoonGreeting2Day4 = '周四午安分享1',
    MorningGreeting1Day4 = '周四早安分享1',
    ClassMeetingNotify1Day4 = '周四课程提醒1',
    ClassMeetingNotify2Day4 = '周四课程提醒2',
    ClassMeetingNotify3Day4 = '周四课程提醒3',
    ClassMeeting1Day4 = '周四直播1',
    ClassMeeting2Day4 = '周四直播2',
    ClassMeeting3Day4 = '周四直播3',
    ClassMeeting4Day4 = '周四直播4',
    ClassMeeting5Day4 = '周四直播5',
    ClassMeeting6Day4 = '周四直播6',
    ClassMeeting7Day4 = '周四直播7',
    ClassMeeting8Day4 = '周四直播8',
    ClassMeeting9Day4 = '周四直播9',
    ClassMeeting10Day4 = '周四直播10',
    ClassMeeting11Day4 = '周四直播11',
    ClassMeeting12Day4 = '周四直播12',
    ClassMeeting13Day4 = '周四直播13',
    ClassMeeting14Day4 = '周四直播14',
    AfterClassMeeting1Day4 = '周四课后分享1',

    MorningGreeting1Day5 = '周五早安分享1',
    MorningGreeting2Day5 = '周五早安分享2',
    NoonGreetingDay5 = '周五午安分享',
    ClassMeetingNotify1Day5 = '周五课程提醒1',
    ClassMeetingNotify2Day5 = '周五课程提醒2',
    ClassMeetingNotify3Day5 = '周五课程提醒3',
    ClassMeetingDay5 = '周五直播',
    ModifyGroupName = '修改群名',
    GroupAnnouncement = '发群公告',
    RemoveFromGroup = '移出群成员'
}

export enum AccountTaskName {
    WeeklyBindPhoneFailNotify = '周一未绑定手机号客户通知'
}
