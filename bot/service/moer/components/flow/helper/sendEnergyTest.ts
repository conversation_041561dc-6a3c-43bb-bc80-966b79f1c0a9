import { SilentReAsk } from '../../schedule/silent_requestion'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { AsyncLock } from '../../../../../lib/lock/lock'
import { SendEnergyTest } from '../schedule/task/sendEnergyTest'
import { TaskName } from '../schedule/type'
import { DataService } from '../../../getter/getData'


//  10 mins 后客户没有消息，进入到发送能量测评，注意客户一旦说话，任务会被取消
export async function sendEnergyTest(chat_id: string, user_id: string) {
  if (ChatStateStore.getFlags(chat_id).is_delayed_send_energy_test) {
    return
  }


  await SilentReAsk.schedule(chat_id, async () => {
    if (ChatStateStore.getFlags(chat_id).is_delayed_send_energy_test) {
      return
    }

    ChatStateStore.update(chat_id, {
      state: {
        is_delayed_send_energy_test: true,
      }
    })

    const lock = new AsyncLock()
    await lock.acquire(chat_id, async () => {
      await new SendEnergyTest().process({
        name: TaskName.SendEnergyTest,
        chatId: chat_id,
        userId: user_id,
      })

    }, { timeout: 3 * 60 * 1000 })

    await DataService.saveChat(chat_id, user_id)
  }, 50 * 60 * 1000, {
    auto_retry: true,
    independent: true
  })
}