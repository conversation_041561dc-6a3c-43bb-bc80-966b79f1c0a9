import { HumanMessage, SystemMessage } from '@langchain/core/messages'
import { z } from 'zod'
import { getUserId } from '../../../../../config/chat_id'
import { Config } from '../../../../../config/config'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { DateHelper } from '../../../../../lib/date/date'
import { JSONHelper } from '../../../../../lib/json/json'
import { ObjectUtil } from '../../../../../lib/object'
import { XMLHelper } from '../../../../../lib/xml/xml'
import logger from '../../../../../model/logger/logger'
import { DataService } from '../../../getter/getData'
import { MergeSlotArrayPrompt } from '../../../prompt/moer/mergeSlot'
import { ExtractUserSlotPrompt } from '../../../prompt/moer/userSlots'
import { ChatStateStore, contentWithFrequency, IChatState, ICustomSlot } from '../../../storage/chat_state_store'
import { ContextBuilder } from '../../agent/context'
import { ChatPromptTemplate, SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { ChatHistoryService } from '../../chat_history/chat_history'
import dayjs from 'dayjs'

export interface ILogInfo {
  [key:string]: any
  chat_id?: string
  user_id?: string
  round_id?: string
}


export class ExtractUserSlots {
  public static async extractUserSlots(chatHistory: string, chat_id: string, logInfo?: ILogInfo) {

    const currentTime = await DataService.getCurrentTime(chat_id)
    if (this.shouldExtractPurchaseHesitation(currentTime)) {
      await ExtractUserSlots.extractPurchaseHesitation(chatHistory, chat_id, logInfo)
    }

    const extractPrompt = await ExtractUserSlotPrompt.format(chatHistory)

    let currentUserSlots = await ExtractUserSlots.extract(extractPrompt, ExtractUserSlotPrompt.schema, logInfo)
    const prevUserSlots = ChatStateStore.get(chat_id).userSlots

    if (currentUserSlots) {
      currentUserSlots = await this.mergeUserSlots(prevUserSlots, currentUserSlots)
      ChatStateStore.update(chat_id, { userSlots: currentUserSlots })
    }

    if (!Config.setting.localTest) {
      await DataService.saveChat(chat_id, getUserId(chat_id))
    }

    return currentUserSlots
  }

  private static async mergeUserSlots(prevUserSlots: Record<string, any>, currentUserSlots: Record<string, any>) {
    const promises: Promise<any>[] = []

    for (const [key, value] of Object.entries(currentUserSlots)) {
      const prevValue = prevUserSlots[key]

      if (Array.isArray(value) && value.length > 0) {
        if (prevValue && Array.isArray(prevValue) && prevValue.length > 0) {
          // 进行合并
          promises.push(this.mergeSlotArray(prevValue as string[], value as string[], key))
        }
      }
    }

    const slots = await Promise.all(promises)

    const mergedObject = slots.reduce((acc, curr) => {
      return ObjectUtil.merge(acc, curr)
    }, {})

    currentUserSlots = ObjectUtil.merge(currentUserSlots, mergedObject)

    // 其他槽位使用 merge
    return ObjectUtil.merge(prevUserSlots, currentUserSlots)
  }


  public static async extract(prompt: string | SystemMessagePromptTemplate, schema: z.ZodType<any, any>, logInfo?: ILogInfo) {
    const llm = new LLM({
      meta: {
        ...logInfo,
        promptName: 'UserSlotsExtraction',
        description: '提取客户槽位'
      }
    })

    const llmRes = await llm.predict(prompt)
    const extractedInfo = XMLHelper.extractContent(llmRes, 'extracted_info')
    if (extractedInfo === null) {
      return null
    }

    try {
      const slotsObj = JSONHelper.parse(extractedInfo)
      const repairedSlots = this.repair(slotsObj)

      const validationResult = schema.safeParse(repairedSlots)
      if (!validationResult.success) {
        logger.error('extractSlots Validation failed', validationResult.error)
        return null
      }

      return repairedSlots
    } catch (e) {
      console.error('error parsing extracted slots', e)
      return null
    }
  }

  private static repair(slots: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {}

    for (const [key, value] of Object.entries(slots)) {
      let repairedValue = value

      if (typeof repairedValue === 'string') {
        repairedValue = repairedValue.trim()
      }

      // 过滤无效的值
      if (!this.isValidValue(repairedValue, key)) {
        continue
      }

      if (['meditation_experience', 'goals_and_needs', 'pain_points', 'purchase_hesitation', 'meditation_practice_experience'].includes(key)) {
        if (typeof repairedValue === 'string') {
          repairedValue = repairedValue.split(/[,，]/)
        }
      }

      // 痛点内过滤掉能量测评提取出的痛点
      if (key === 'pain_points') {
        if (Array.isArray(repairedValue)) {
          repairedValue = repairedValue.filter((v) => !(v.includes('疲倦') && v.includes('缺乏动力')))
        }
      }

      result[key] = repairedValue
    }

    return result
  }

  private static isValidValue(value: any, key: string): boolean {
    return value !== 0 &&
            value !== null &&
            !(Array.isArray(value) && value.length === 0) &&
            value !== '未知' &&
            value !== ''
  }

  public static formatSlots(slots: Record<string, any>, translations: Record<string, string>) {
    let formattedInfo = ''

    for (const [key, value] of Object.entries(slots)) {
      if (!value) continue

      const translatedKey = translations[key] || key
      const formattedValue = this.formatValue(key, value)

      formattedInfo += `${translatedKey}：${formattedValue}\n`
    }

    return formattedInfo.trim()
  }

  private static formatValue(key: string, value: any): string {
    return `${value}`
  }

  private static async mergeSlotArray(prevValue: string[], value: string[], key: string) {
    const llm = new LLM({
      meta: {
        promptName: 'MergeSlotArray',
        description: '合并槽位数组'
      }
    })

    const slotDescriptions = {
      'meditation_experience': 'User\'s previous meditation experience, if any. Include both whether they have experience and what methods they\'ve used if mentioned.',
      'goals_and_needs': 'User\'s stated goals for meditation',
      'pain_points': 'Issues and emotions user aims to address through meditation. This includes specific life challenges, health concerns, emotional distress, and any other significant barriers that the user has mentioned.',
      'purchase_hesitation': 'User\'s concerns or hesitations about purchasing the course. This may include financial concerns, time constraints, or other factors that are preventing them from making a purchase.',
      'meditation_practice_experience': 'User\'s emotional or physical sensations and experiences during meditation practice, including feelings of calm, discomfort, stress, or any specific thoughts or body reactions.'
    }

    const prompt = await MergeSlotArrayPrompt.format(key, slotDescriptions[key], [prevValue, value])
    const llmRes = await llm.predict(prompt)
    const mergedResult = XMLHelper.extractContent(llmRes, 'merged_result')

    if (mergedResult === null) {
      return {
        [key]: Array.from(new Set([...prevValue, ...value]))
      }
    }

    return {
      [key]: JSONHelper.parse(mergedResult)
    }
  }

  static async extractPurchaseHesitation(chatHistory: string, chat_id: string, logInfo?: ILogInfo) {

    const extractResult = await this.extractStuckPoint(chatHistory, chat_id, logInfo)

    const mergedResult = await this.mergesStuckPoint(extractResult, chat_id, logInfo)

    ChatStateStore.update(chat_id, {
      userSlots:{
        purchaseHesitation: mergedResult.split('，')
      }
    })

  }

  static async extractStuckPoint(chatHistory: string, chat_id: string, logInfo?: ILogInfo) {
    const promptTemplate = ExtractUserSlotPrompt.extractStuckPointPrompt()

    const llmRes = await LLM.predict(promptTemplate, { meta: { promptName: 'extract_purchase_hesitation', chat_id: chat_id, round_id: logInfo?.round_id }, temperature: 0 }, { chatHistory })

    const content = XMLHelper.extractContent(llmRes, 'purchaseHesitation')

    if (!content) {
      logger.trace('提取客户卡点失败：解析xml错误或当前客户无卡点')
      return ''
    }

    return content
  }

  static async mergesStuckPoint(newStuckPoint: string, chat_id: string, logInfo?: ILogInfo) {
    const promptTemplate = ExtractUserSlotPrompt.mergeStuckPointPrompt()

    const purchaseHesitation = ChatStateStore.get(chat_id).userSlots.purchaseHesitation
    let oldStuckPoint = ''
    if (purchaseHesitation) {
      oldStuckPoint = purchaseHesitation.join('-')
    }

    let llmRes = await LLM.predict(promptTemplate, { meta: { promptName: 'merge_purchase_hesitation', chat_id: chat_id, round_id: logInfo?.round_id }, temperature: 0 }, { newStuckPoint, oldStuckPoint })

    const think = XMLHelper.extractContent(llmRes, 'think')
    if (think) {
      llmRes = llmRes.replace(think, '')
    }

    const content = XMLHelper.extractContent(llmRes, 'result')

    if (!content) {
      logger.trace('合并客户卡点失败：解析xml错误或当前客户无卡点')
      return ''
    }

    return content
  }

  static shouldExtractPurchaseHesitation(currentTime: any): boolean {
    return (
      (
        currentTime.is_course_week &&
            (
              currentTime.day > 3 ||
                (currentTime.day === 3 && DateHelper.isTimeAfter(currentTime.time, '21:00:00'))
            )
      ) ||
        currentTime.post_course_week
    )
  }
}

export type ChatHistoryWithRoleAndDate = {
  role: 'user' | 'assistant'
  date: string
  message: string
}

export class UserSlot {
  topic: string
  subTopic: string
  content: string
  frequency: number

  constructor(topic: string, subTopic: string, content: string, frequency: number = 1) {
    this.topic = topic
    this.subTopic = subTopic
    this.content = content
    this.frequency = frequency
  }

  static async fromString(input:string, logInfo?: ILogInfo): Promise<UserSlot | null> {
    const trimedInput = input.trim()
    if (trimedInput == '') return null
    if (!this.validateFormat(trimedInput)) {
      return await this.repairSlotForm(trimedInput, logInfo)
    }
    return this.fromCorrectInput(trimedInput)
  }

  private static fromCorrectInput(input: string): UserSlot| null {
    const trimedInput = input.trim()
    const originText = trimedInput.substring(2)
    const splited = originText.split('::')
    if (splited.length < 3) return null
    return new UserSlot(splited[0].trim(), splited[1].trim(), splited.slice(2).join(',').trim())
  }

  private static async repairSlotForm(input: string, logInfo?: ILogInfo): Promise<UserSlot | null> {
    const llm = new LLM({
      meta: {
        ...logInfo,
        promptName: 'RestoreOriginalSentenceForSlotExtraction',
      },
    })
    const response = await llm.predictMessage([new SystemMessage(getRepairPrompt()), new HumanMessage(input)])
    if (!this.validateFormat(response)) {
      logger.error(`槽位初始语句修复失败，语句为:${response},logInfo:${JSON.stringify(logInfo)}`)
      return null
    } else {
      return this.fromCorrectInput(response)
    }

    function getRepairPrompt():string {
      return `你是一位善于分析文本内容的专家。我将提供给你一段文本，请你认真分析，并按照以下格式进行输出：
## 格式
### 输出
你需要从对话中提取事实和偏好，并输出：
- TOPIC::SUB_TOPIC::MEMO
例如：
- 基本信息::姓名::melinda

输出一行，包括一个事实或偏好，包含：
1. TOPIC: 主题，表示该偏好的类别
2. SUB_TOPIC: 详细主题，表示该偏好的具体类别
3. MEMO: 提取的信息、事实或偏好

请先提炼出文本的核心主题，再根据主题明确地划分出相关的子主题，最后对子主题详细概括相应的内容。
请直接给出最终结果，无需解释分析过程。

## 注意
- 请只输出一个主题/子主题
- 请严格按照格式输出`
    }
  }

  private static validateFormat(input: string) :boolean {
    const trimedInput = input.trim()
    if (trimedInput.length < 2 && !trimedInput.startsWith('- ')) {
      return false
    }
    if (trimedInput.split('::').length < 3) return false

    return true
  }
}

export class UserSlots {
  slots: Record<string, Record<string, contentWithFrequency>> = {}
  constructor(userSlots: UserSlot[]) {
    for (const slot of userSlots) {
      this.add(slot)
    }
  }

  isTopicExist(topic:string):boolean {
    if (!this.slots[topic]) {
      return false
    } else {
      return true
    }
  }

  isTopicSubTopicExist(topic:string, subTopic:string):boolean {
    if (!this.slots[topic]) {
      return false
    }
    if (!this.slots[topic][subTopic]) {
      return false
    }
    return true
  }

  getStringByTopic(topic: string):string {
    if (!this.slots?.[topic]) return ''
    const res = Object.entries(this.slots[topic]).map(([key, value]) => `${key}::${value.content}`).join('，')
    return `${topic}：${res}`
  }

  static async fromString(str:string, logInfo?:ILogInfo):Promise<UserSlots> {
    if (!str) {
      return new UserSlots([])
    }

    if (str.includes ('NONE')) {
      return new UserSlots([])
    }

    const slotOriginList = str.split ('\n').map ((item) =>
      UserSlot.fromString (item, logInfo)
    )

    const slotList = (await Promise.all (slotOriginList)).filter ((slot) => slot !== null)
    return new UserSlots(slotList as unknown as UserSlot[])

  }

  static fromRecord(record: Record<string, contentWithFrequency>):UserSlots {
    const userSlotList:UserSlot[] = []
    for (const key in record) {
      const splitedKey = key.split('::')
      if (splitedKey.length != 2) {
        logger.error(`解析错误, ${key}`)
      } else {
        userSlotList.push({
          topic: splitedKey[0],
          subTopic: splitedKey[1],
          content: record[key].content,
          frequency: record[key].frequency
        })
      }
    }
    return new UserSlots(userSlotList)
  }

  toString(): string {
    let res: string = ''
    const importantThreshold = this.getImportantThreshold()
    for (const topic in this.slots) {
      res += `- ${topic}\n`
      for (const subTopic in this.slots[topic]) {
        const { content, frequency } = this.slots[topic][subTopic]
        res += `  - ${subTopic}`
        if (frequency > importantThreshold) {
          res += '（多次提及）'
        }
        res += `：${content}\n`
      }
    }
    return res.trim()
  }

  getImportantThreshold(): number {
    let totalFrequency = 0
    let totalSubTopic = 0
    for (const topic in this.slots) {
      for (const subTopic in this.slots[topic]) {
        const { frequency } = this.slots[topic][subTopic]
        totalSubTopic += 1
        totalFrequency += frequency
      }
    }
    //比这个多的就是比较重要的
    const aveFrequency = totalFrequency / totalSubTopic
    let variance = 0
    for (const topic in this.slots) {
      for (const subTopic in this.slots[topic]) {
        const { frequency } = this.slots[topic][subTopic]
        variance += (frequency - aveFrequency) * (frequency - aveFrequency)
      }
    }
    const std = Math.sqrt(variance / totalSubTopic)
    const threshold = aveFrequency + 1.5 * std
    return threshold
  }

  toList():ICustomSlot[] {
    const list:ICustomSlot[] = []
    for (const topic in this.slots) {
      for (const subTopic in this.slots[topic]) {
        list.push({
          topic: topic,
          subTopic: subTopic,
          content: this.slots[topic][subTopic].content,
          frequency: this.slots[topic][subTopic].frequency
        })
      }
    }
    return list
  }

  getTopicAndSubTopic(): Record<string, Array<string>> {
    const res:Record<string, Array<string>> = {}
    for (const topic in this.slots) {
      if (!res[topic]) res[topic] = []
      for (const subTopic in this.slots[topic]) {
        res[topic].push(subTopic)
      }
    }
    return res
  }

  toRecord():Record<string, contentWithFrequency> {
    const res:Record<string, contentWithFrequency> = {}
    for (const topic in this.slots) {
      for (const subTopic in this.slots[topic]) {
        const content = this.slots[topic][subTopic]
        res[`${topic}::${subTopic}`] = {
          content: content.content,
          frequency: content.frequency
        }
      }
    }
    return res
  }

  add(slot: UserSlot) {
    if (!this.slots[slot.topic]) {
      this.slots[slot.topic] = {}
    }
    if (!this.slots[slot.topic][slot.subTopic]) {
      this.slots[slot.topic][slot.subTopic] = { content:slot.content, frequency:slot.frequency }
    } else {
      this.slots[slot.topic][slot.subTopic].content += `。${slot.content}`
    }
  }

  async forget(logInfo?:ILogInfo) {
    const llm = new LLM ({
      model:'o4-mini',
      meta: {
        ...logInfo,
        promptName: 'UserSlotForget',
        description: '客户槽位遗忘'
      },
    })
    const systemPrompt = `你是一位专业的心理学家，你有一段客户画像。你的责任是仔细阅读客户画像，将客户画像在尽量和原本画像主题保持一致的情况下，将客户中的重要信息提取出来，将不重要的信息遗忘。

## 输入格式
- 主题
 - 子主题：具体内容
 - 子主题：具体内容
- 主题
 - 子主题：具体内容
 - 子主题：具体内容

## 输出格式
### 输出
请先在 <think></think> 标签中结合客户对话，主题建议和已有的主题按照以下步骤分析这段对话需要应当将什么重要的信息保留，并分析什么信息需要被遗忘。
1. 将主题和子主题分为三个类别，非常重要，中等重要，不重要。
2. 将中等重要的信息进行总结归类压缩。
3. 将非常重要的信息和总结归类后的中等重要的信息保留，将不重要的信息遗忘。
然后在 <result></result> 标签中结合think和对话中提取需要保留的事实和偏好，并按顺序列出：
- TOPIC::SUB_TOPIC::MEMO
例如：
- 工作::职称::软件工程师

每行代表一个事实或偏好，包含：
1. TOPIC: 主题，表示该偏好的类别
2. SUB_TOPIC: 详细主题，表示该偏好的具体类别
3. MEMO: 提取的信息、事实或偏好
这些元素应以::分隔，每行应以 :: 分隔，并以 "- " 开头。

## 画像提取和遗忘规则
1. 和冥想有关的信息是重要的
2. 多次提及的信息是重要的
3. 时间信息和课程安排是需要被遗忘的
4. 客户的咨询和问题是需要被遗忘的

## 建议保留的主题
以下是一些主题和子主题的建议，你需要参考这些主题和子主题来保留信息。
- 基本信息：客户年龄（整数），性别，居住地，生活角色
- 兴趣爱好：书籍，电影，音乐，美食，运动
- 工作：职位，工作技能
- 过往冥想经验 
- 痛点：情绪焦虑，经济压力，家庭矛盾
- 冥想目标（明确提及的冥想目标）：改善睡眠，缓解压力
- 冥想课后感受（冥想入门营课后感受）
- 购买意向：系统班（客户是否对系统班感兴趣）`
    const userSlots = this.toString().replaceAll('## 客户画像\n', '')

    const preResponse = await llm.predictMessage([new SystemMessage(systemPrompt), new HumanMessage(userSlots)])
    const extractResponse = XMLHelper.extractContent(preResponse, 'result')
    const newUserSlots = (await UserSlots.fromString(extractResponse ?? '')).slots
    for (const topic in newUserSlots) {
      for (const subTopic in newUserSlots[topic]) {
        if (this.slots?.[topic]?.[subTopic]?.frequency) {
          newUserSlots[topic][subTopic].frequency = this.slots[topic][subTopic].frequency
        }
      }
    }
    this.slots = newUserSlots
  }

  async merge(another: UserSlots, logInfo?: ILogInfo)  {
    const newUserSlot = await _merge(this, another, logInfo)
    for (const topic in another.slots) {
      for (const subTopic in another.slots[topic]) {
        const newMemo = another.slots[topic][subTopic]
        if (!this.slots[topic]) {
          this.slots[topic] = {}
        }
        if (!this.slots[topic][subTopic]) {
          this.slots[topic][subTopic] = newMemo
        } else {
          this.slots[topic][subTopic].frequency += 1
        }
      }
    }
    for (const { topic, subTopic, content } of newUserSlot) {
      this.slots[topic][subTopic].content = content
    }

    async function _merge(oldMemo:UserSlots, newMemo:UserSlots, logInfo?: ILogInfo):Promise<UserSlot[]> {
      const llm = new LLM({
        meta: {
          ...logInfo,
          promptName: 'MergeSlotArrayV2',
          description: '提取客户槽位v2'
        },
      })
      const promptTemplate = ChatPromptTemplate.fromMessages([
        SystemMessagePromptTemplate.fromTemplate(getMergePrompt()),
        ['user', '{memo}']
      ])
      const memo = formatMemo(oldMemo, newMemo)
      if (memo == '') {
        return []
      }
      const response = await llm.predictMessage(promptTemplate, { memo:memo })
      const slotOriginList = response.split ('\n').map ((item) =>
        UserSlot.fromString (item, logInfo)
      )

      const slotList = (await Promise.all (slotOriginList)).filter ((slot) => slot !== null)
      return slotList as unknown as UserSlot[]
    }

    function formatMemo(oldMemo:UserSlots, newMemo:UserSlots):string {
      const topicSubtopicArray:Record<string, Array<string>> = {}
      for (const topic in oldMemo.slots) {
        for (const subTopic in oldMemo.slots[topic]) {
          if (newMemo.isTopicSubTopicExist(topic, subTopic)) {
            if (!topicSubtopicArray[topic]) topicSubtopicArray[topic] = []
            topicSubtopicArray[topic].push(subTopic)
          }
        }
      }
      let res:string = ''
      for (const topic in topicSubtopicArray) {
        res += `- ${topic}\n`
        for (const subTopic of topicSubtopicArray[topic]) {
          res += ` - ${subTopic}\n`
          res += `  - 旧备忘录：${oldMemo.slots[topic][subTopic].content}\n`
          res += `  - 新备忘录：${newMemo.slots[topic][subTopic].content}\n`
        }
      }
      return res
    }

    function getMergePrompt(): string {
      return `你是一个智能备忘录管理器，负责控制客户的记忆/形象。
你将收到关于客户不同主题/方面的备忘录，关于每个不同的主题/方面，有一条是旧的，一条是新的。
你应更新旧的备忘录，以包含新的备忘录中的信息。
并以输出格式返回你的结果：
最终的MEMO备忘录(每个主题/方面5句话以内)。

以下是如何生成最终的备忘录的指导原则：
## 替换旧备忘录
如果新备忘录与旧备忘录完全冲突，你应该用新的备忘录替换旧的备忘录。
**示例**：
INPUT:
- 基本信息
 - 年龄
  - 旧备忘录：客户39岁
  - 新备忘录：客户40岁
 - 居住地
  - 旧备忘录：北京
  - 新备忘录：上海
OUTPUT:
- 基本信息::年龄::客户39岁，客户40岁
- 基本信息::居住地::北京，上海

## 合并备忘录
如果旧备忘录中包含新备忘录中没有的信息，你应该将旧备忘录和新备忘录合并。
你需要总结新旧备忘录的内容，以便在最终备忘录中包含充分的信息。
**示例**：
INPUT:
- 个性
 - 情绪反应
  - 旧备忘录：下雨天客户有时会哭泣
  - 新备忘录：下雨天客户会想起了家乡
- 心理特征
 - 价值观
  - 旧备忘录：坚持学习，活到老学到老，获到老是客户一生的信念
  - 新备忘录：坚持学习，活到老学到老
OUTPUT:
 - 个性::情绪反应::下雨天客户会想起家乡，可能是其下雨天哭泣的原因之一
 - 心理特征::价值观::坚持学习，活到老学到老是客户的核心价值观，体现了其对终身学习的高度重视。

理解备忘录，你可以从新备忘录和旧备忘录中推断信息以决定正确的操作。
遵循以下说明：
- 不要返回上面提供的自定义少量提示中的任何内容。
- 严格遵守正确的格式。
- 保持备忘录的简洁性`
    }
  }
}


export function chatHistoryWithRoleAndDateListToString(chatHistory:ChatHistoryWithRoleAndDate[]):string {
  return `<chat_history>
${chatHistory.map((item) => {
    return `- [${item.date}] ${item.role}: ${item.message}\n`
  }).join('')}</chat_history>`
}
export class ExtractUserSlotsV2 {
  static async extractUserSlots(chatId: string, round:number = 6, logInfo?: ILogInfo):Promise<UserSlots> {
    let chatHistoryRaw = await ChatHistoryService.getRecentConversations(chatId, round, 'user')
    if (chatHistoryRaw.length > 20) {
      chatHistoryRaw = chatHistoryRaw.slice(-20)
    }
    const chatHistory = chatHistoryRaw.map((message) => ({ role:message.role, date:dayjs(message.created_at).format('YYYY/MM/DD HH:mm:ss'), message:message.content }))
    return await this.extractUserSlotsFromChatHistory(
      chatHistory,
      chatId,
      false,
      undefined,
      logInfo,
    )
  }
  static async extractUserSlotsFromChatHistory(chatHistory: ChatHistoryWithRoleAndDate[], chatId: string, isDanmu:boolean, model:string = 'o4-mini', logInfo?: ILogInfo):Promise<UserSlots> {
    const currentTime = await DataService.getCurrentTime(chatId)
    if (ExtractUserSlots.shouldExtractPurchaseHesitation(currentTime)) {
      await ExtractUserSlots.extractPurchaseHesitation(chatHistoryWithRoleAndDateListToString(chatHistory), chatId)
    }
    const chatState = ChatStateStore.get(chatId)
    const prevCustomUserSlots = this.getCustomSlotByIsSecret(chatState, isDanmu)
    const previousTopicAndSubTopic = prevCustomUserSlots.getTopicAndSubTopic()
    const currentCustomUserSlots = await this.extract(chatId, chatHistory, previousTopicAndSubTopic, model, logInfo)

    if (prevCustomUserSlots) {
      await prevCustomUserSlots.merge(currentCustomUserSlots, logInfo)
      if (prevCustomUserSlots.toString().length > 800) {
        await prevCustomUserSlots.forget()
      }
      await persistUserCustomSlot(chatId, prevCustomUserSlots, isDanmu)
      return prevCustomUserSlots
    } else {
      await persistUserCustomSlot(chatId, currentCustomUserSlots, isDanmu)
      return currentCustomUserSlots
    }

    async function persistUserCustomSlot(chatId: string, customUserSlots:UserSlots, isDanmu: boolean) {
      if (isDanmu) {
        ChatStateStore.update(chatId, {
          danmuUserSlots:customUserSlots.toRecord()
        })
      } else {
        ChatStateStore.update(chatId, {
          moreUserSlots:customUserSlots.toRecord()
        })
      }
      if (!Config.setting.localTest || chatId.includes('local')) {
        await DataService.saveChat(chatId, getUserId(chatId))
      }
    }
  }
  static getCustomSlotByIsSecret(chatState:IChatState, isSecret: boolean):UserSlots {
    if (isSecret) {
      return UserSlots.fromRecord(chatState.danmuUserSlots ?? {})
    } else {
      return UserSlots.fromRecord(chatState.moreUserSlots ?? {})
    }
  }

  public static async extract (
    chatId: string,
    chatHistory: ChatHistoryWithRoleAndDate [],
    previousTopicAndSubTopic: Record<string, Array<string>>,
    model:string,
    logInfo?: ILogInfo
  ): Promise<UserSlots> {
    const bootCampBasicSetting = ContextBuilder.getBootCampBasic()
    const stagePromptData = ContextBuilder.getBriefStagePromptData()
    const previousTopicAndSubTopicPrompt = this.topicAndSubTopicIntoPrompt(previousTopicAndSubTopic)
    const stagePrompt = await ContextBuilder.getStagePrompt(chatId, stagePromptData)
    return await this.rawExtract({ chatHistory, bootCampBasicSetting, stagePrompt, previousTopicAndSubTopicPrompt, model, logInfo })
  }

  static async rawExtract({ chatHistory, bootCampBasicSetting, stagePrompt, previousTopicAndSubTopicPrompt, logInfo, model = 'o4-mini' }:{chatHistory:ChatHistoryWithRoleAndDate []; bootCampBasicSetting:string; stagePrompt:string; previousTopicAndSubTopicPrompt:string; logInfo?:ILogInfo;model?:string}):Promise<UserSlots> {
    const llm = new LLM ({
      model: model,
      maxTokens: 5000,
      meta: {
        ...logInfo,
        promptName: 'ExtractSlotArrayV2',
        description: '提取客户槽位v2'
      },
    })
    const systemPrompt = await this.getExtractPrompt ()
    const chatHistoryString = chatHistoryWithRoleAndDateListToString (chatHistory)
    const promptTemplate = ChatPromptTemplate.fromMessages([
      systemPrompt,
      ['user', '{chat_history}']
    ])

    const preResponse = await llm.predictMessage (promptTemplate, { chat_history: chatHistoryString, bootCampBasicSetting:bootCampBasicSetting, stagePrompt:stagePrompt, previousTopicAndSubTopicPrompt:previousTopicAndSubTopicPrompt })
    const llmResponse = XMLHelper.extractContent(preResponse, 'result')
    return UserSlots.fromString(llmResponse ?? '')
  }

  static async getExtractPrompt():Promise<SystemMessagePromptTemplate> {
    return SystemMessagePromptTemplate.fromTemplate(`你是一位专业的心理学家，你在向你的客户推荐冥想课程。
你的责任是仔细阅读客户与其他方的对话。然后提取相关且重要的事实，这些信息将有助于评估客户的状态。

{bootCampBasicSetting}

{stagePrompt}

## 格式
### 输入
#### 主题建议
这个章节里会放一些主题和子主题的建议，你需要参考这些主题和子主题来提取信息。
如果你认为有必要，可以创建自己的主题/子主题，任何有助于评估客户状态的信息都是受欢迎的。
#### 已有的主题
这个章节中会放客户已经与助手分享的主题和子主题
如果对话中再次提到相同的主题/子主题，请考虑使用相同的主题/子主题。
#### 对话
输入的格式是客户和另一方的对话:
- [TIME] NAME: MESSAGE
其中NAME有时候是user，有时候是assistant。
TIME未知将不会显示，但对话顺序是正确的
MESSGAE则是对话内容. 理解对话内容并且记住事情发生的时间

### 输出
请先在 <think></think> 标签中结合客户对话，主题建议和已有的主题分析这段对话需要应当输出什么主题和子主题。
然后在 <result></result> 标签中直接从think和对话中提取事实和偏好，并按顺序列出：
- TOPIC::SUB_TOPIC::MEMO
例如：
- 工作::职称::软件工程师

每行代表一个事实或偏好，包含：
1. TOPIC: 主题，表示该偏好的类别
2. SUB_TOPIC: 详细主题，表示该偏好的具体类别
3. MEMO: 提取的信息、事实或偏好
这些元素应以::分隔，每行应以 :: 分隔，并以 "- " 开头。

请按上述格式返回事实和偏好。

请记住以下几点：
- 如果客户有提到时间敏感的信息，试图推理出具体的日期.
- 严格避免使用相对时间表达，如"昨天"、"今天"、"明天"、"上周"、"下周"等，这些表达会导致时间上下文混乱。如果客户提到具体的时间信息，请使用绝对时间或具体日期。
- 如果在以下对话中没有找到任何相关信息，可以返回空列表。
- 确保按照格式和示例部分中提到的格式返回响应。
- 如果内容中的主体不是客户，应该在主题中标出。
- 如果与主题建议不匹配，请生成一个新的主题而不是使用主题建议中的主题。
- 相同的内容不需要在不同的 topic 和 sub_topic 下重复，选择最相关的主题和子主题即可。
- 相同的 topic 和 sub_topic 只能出现一次。
- 不应将assistant说的内容推理为客户的状态。
- 客户回复好滴，表情不代表客户同意或积极参与，应忽略。
忽视客户(user)对其他方(assistant)的称呼：比如客户称呼其他方(assistant)为小姨，因为对其他方的称呼不一定代表真实的关系，不需要推断客户有一个小姨， 只需要记录客户称呼其他方为小姨即可。

以下是客户和助手之间的对话。你需要从对话中提取/推断相关的事实和偏好，并按上述格式返回。
请注意，你要准确地提取和推断客户相关(user)的信息，而非其他方(assistant)的。
你应该检测客户输入的语言，并用相同的语言记录事实。如果在以下对话中没有找到任何相关事实、客户记忆和偏好，你可以返回"NONE"。

#### 主题建议
以下是一些主题和子主题的建议，你需要参考这些主题和子主题来提取信息。
- 基本信息：客户年龄（整数），性别，居住地，生活角色
- 兴趣爱好：书籍，电影，音乐，美食，运动
- 工作：职位，工作技能
- 过往冥想经验（客户以前的冥想经验，包括是否有经验和使用过的方法，冥想入门营的感受不应分类为此类） 
- 痛点：情绪焦虑，经济压力，家庭矛盾
- 冥想目标（明确提及的冥想目标）：改善睡眠，缓解压力
- 冥想课后感受（冥想入门营课后感受）：第一节课感受，第二节课感受，第三节课感受，第四节课感受
- 购买意向：系统班（客户是否对系统班感兴趣）

#### 主题规则
- 不需要提取是否报名“五天冥想入门营”
- 不需要提取课程进度，观看情况，参与安排和课程完成情况，课程如如冥想入门营，先导课，小讲堂，第一节课，第二节课，第三节课
- 不需要提取客户的咨询或技术问题
- 购买意向::系统班：可以通过客户是否询问系统班相关信息来判断是否对系统班感兴趣


{previousTopicAndSubTopicPrompt}`
    ) }

  static topicAndSubTopicIntoPrompt(topicAndSubtopic: Record<string, Array<string>>): string {
    let res = ''
    for (const topic in topicAndSubtopic) {
      res += `- ${topic}：`
      topicAndSubtopic[topic].forEach((value, index) => {
        if (index != 0) {
          res += '，'
        }
        res += value
      })
      res += '\n'
    }
    if (res != '') {
      res = `#### 已有的主题\n${  res}`
    }
    return res
  }
}
