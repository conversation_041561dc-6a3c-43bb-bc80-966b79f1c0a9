import { ContextBuilder } from '../../../agent/context'
import { DataService } from '../../../../getter/getData'
import { ChatStatStoreManager } from '../../../../storage/chat_state_store'


describe('PromptTest', function () {
  const chat_id = '7881301655007754_1688857949631398'
  beforeAll(async () => {
    await ChatStatStoreManager.initState(chat_id)
  })
  it('1.TestGetCourseSummaryInfo', async () => {
    // const user_id = UUID.short()
    // const chat_id = getChatId(user_id)
    const chats = await DataService.getChatByWechatName('夏青 金牌喜嫁鲜花💐 鸿锦婚礼')
    if (!chats) {
      return
    }
    const chat =  chats[0] as any
    const chatId = chat._id
    await ChatStatStoreManager.initState(chatId)
    console.log(await ContextBuilder.getCustomerBehavior(chatId))
  }, 60000)

  it('get-user-slots', async () => {
    const chat_id = '7881302298050442_1688858254705213'
    // const user_id = UUID.short()
    // const chat_id = getChatId(user_id)
    await ChatStatStoreManager.initState(chat_id)
    console.log(ContextBuilder.getCustomerPortrait(chat_id))
  })

  it('2. 测试getRule函数', async () => {
    console.log(await ContextBuilder.getRules('test'))
  })


  it('3. 测试getStagePrompt', async () => {
    const stagePromptData = await ContextBuilder.getDefaultStagePromptData(chat_id)
    if (stagePromptData) {
      const stagePrompt = await ContextBuilder.getStagePrompt(chat_id, stagePromptData)
      console.log(stagePrompt)
    } else {
      console.log('获取阶段prompt失败')
    }
  })

  it('4. 测试getStagePrompt brief版本', async () => {
    const stagePromptData = ContextBuilder.getBriefStagePromptData()
    const stagePrompt = await ContextBuilder.getStagePrompt(chat_id, stagePromptData)
    console.log(stagePrompt)
  })
})