export enum MoerNode {
    FreeTalk = 'free_talk', // 闲聊节点
    PhoneQuery = 'phone_query',
    IntentionQuery = 'intention_query',
    IntentionQueryBeforeClass = 'intention_query_before_class',
    EnergyTestAnalyze = 'energy_test_analyze',
    CourseFeedBackDay1 = 'course_feedback_day1',
    CourseFeedBackDay2 = 'course_feedback_day2',
    Sales = 'sales',
    WealthOrchardAnalyze = 'wealth_orchard_analyze',
    PostSale = 'post_sale',
    SendFile = 'send_file',
    Dummy = 'dummy', // 占位假节点，在路由的时候 return 这个节点，表示在路由期间直接执行逻辑，不再进行后续路由判断。会继续执行上次的节点
    DummyEnd = 'dummy_end',  // 占位假节点，在路由的时候 return 这个节点，表示在路由期间已经执行完所有逻辑，不执行后续节点的逻辑
    SendInviteLink = 'send_product_link',
    CourseFeedBackDay3NotInClass =  'course_feedback_day3_not_in_class',
    CourseFeedBackDay3InClass = 'course_feedback_day3_in_class',
    RespondCourseDate = 'respond_course_date_node'
}