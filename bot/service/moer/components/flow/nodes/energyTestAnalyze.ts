import { MoerWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { MoerNode } from './type'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { LLMNode } from './llm'
import { FreeTalk } from '../../agent/freetalk'
import { DataService } from '../../../getter/getData'
import { sleep } from '../../../../../lib/schedule/schedule'
import { MessageSender } from '../../message/message_send'
import { ContextBuilder } from '../../agent/context'

export class EnergyTestAnalyze extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const testScore = await DataService.getEnergyTestScore(state.chat_id)
    if (!testScore) {
      return await FreeTalk.invoke(state)
    }

    // 如果完成了 能量测评解读，跳转到 FreeTalk
    const isCompleteEnergyTestAnalyze = ChatStateStore.getFlags(state.chat_id).is_complete_energy_test_analyze
    if (isCompleteEnergyTestAnalyze) {
      return await FreeTalk.invoke(state)
    }

    /**
     * >= 200 分进行鼓励肯定
     * < 200 分，进行询问，然后进行反馈
     */
    if (testScore >= 200) {
      await this.handleHighScore(state)
    } else {
      await this.handleLowScore(state)
    }

    return MoerNode.EnergyTestAnalyze
  }

  private static async handleHighScore(state: IWorkflowState) {

    ChatStateStore.update(state.chat_id, {
      state: {
        is_complete_energy_test_analyze: true
      }
    })

    // 进行鼓励和肯定
    await sleep(7000)
    await MessageSender.sendById({
      user_id: state.user_id,
      chat_id: state.chat_id,
      ai_msg: '咱的能量水平已经很不错了，比大部分人都高。继续加油，咱们后面的课程还能帮您再提升一些，到时候再测一次，看看会不会有惊喜'
    })
  }

  private static async handleLowScore(state: IWorkflowState) {
    const nodeCount = ChatStateStore.getNodeCount(state.chat_id, 'LowScoreAsk')
    const userSlots = await ContextBuilder.getCustomerPortrait(state.chat_id)
    // 1. 询问
    if (nodeCount === 0) {

      ChatStateStore.update(state.chat_id, {
        state: {
          is_complete_energy_test_analyze: true
        }
      })

      if (userSlots) {
        await sleep(5000)
        await LLMNode.invoke({
          state,
          dynamicPrompt: `当前已经解释过分数，需要进一步说明200以下属于低能量，询问能量水平低可能带来感受，请结合客户画像，重点参考痛点。引导到后续的冥想课程会帮助解决问题
例如：“结合您之前提到的[痛点1]和[痛点2]，这种低能量可能会让您感到特别疲倦、缺乏动力，没有明确的目标，甚至出现烦躁或紧张的情绪。”“当遇到[具体痛点场景]时,是不是会觉得特别吃力?”“在处理[痛点问题]时,会不会感到力不从心?”`,
          referenceChatHistory: true,
          noCounselorPreFix: true,
          noSplit: true,
        })
      } else {
        await sleep(5000)
        await LLMNode.invoke({
          state,
          dynamicPrompt: '当前已经解释过分数，需要进一步说明200以下属于低能量，询问能量水平低可能带来感受：例如：咱们能量比较低，可能会觉得特别容易疲倦、缺乏动力，没有明确的目标，甚至有点烦躁或紧张。最近有这样的感觉吗？',
          referenceChatHistory: true,
          noCounselorPreFix: true,
          noSplit: true
        })
      }

      ChatStateStore.increNodeCount(state.chat_id, 'LowScoreAsk')
    } else {
      // 2. 进行反馈
      if (userSlots) {
        await LLMNode.invoke({
          state,
          dynamicPrompt: `基于客户画像，尤其是主要痛点，并参考冥想经验和目标需求，分析其内在成因，最后针对性输出冥想作为解决方案的建议：

1. 首先确认和同理客户的困扰
2. 解释这些困扰背后的心理机制
3. 引入冥想作为解决方案，说明其作用原理
4. 具体阐述冥想如何帮助改善客户的特定问题，特定问题可以参考主要痛点和目标需求
5. 给予希望和鼓励

回复语气要温和共情，通过具体例子说明，并强调循序渐进的改变过程。可以引用专业观点增加可信度，如唐宁老师的见解等。最后的输出要是完整且连贯的一段话，不要输出markdown格式！
举例：当客户反馈最近在经历忧虑和焦虑的时候，回复可以是：
"我理解这种压力带来的困扰。唐宁老师说过：“焦虑是对未来的过度担忧，抑郁则是内心深处的阴霾。我们现在的环境是很容易引起焦虑的，但是重要的是通过练习让自己有高能量来应对。“其实，冥想是个非常好的方式。通过冥想，你可以开始放下这些沉重的情绪包袱，发现面对挑战时的内在力量。当你开始思考这些情绪是如何影响你的能量状态时，你就能慢慢地用更积极、更强大的情绪来取代它们。这种变化会逐渐地作用于你的外部世界，帮助你找到克服困难的新路径。冥想不是逃避现实的手段，而是一种深入自我、理解复杂情绪的方式，当你看清楚自己，理清楚自己，就会逐步促成你生活中的积极转变！"`,
          referenceChatHistory: true,
          temperature: 1,
          noCounselorPreFix: true
        })
      } else {
        await LLMNode.invoke({
          state,
          dynamicPrompt: `基于前面客户目前的反馈，针对性和客户输出冥想对其可能带来的价值。
例如：当客户反馈最近在经历忧虑和焦虑的时候；我们需要解读其成因，告知其借着冥想可以优化的部分。唐宁老师说过：“焦虑是对未来的过度担忧，抑郁则是内心深处的阴霾。我们现在的环境是很容易引起焦虑的，但是重要的是通过练习让自己有高能量来应对。”
其实，冥想是个非常好的方式。通过冥想，你可以开始放下这些沉重的情绪包袱，发现面对挑战时的内在力量。当你开始思考这些情绪是如何影响你的能量状态时，你就能慢慢地用更积极、更强大的情绪来取代它们。这种变化会逐渐地作用于你的外部世界，帮助你找到克服困难的新路径。冥想不是逃避现实的手段，而是一种深入自我、理解复杂情绪的方式，当你看清楚自己，理清楚自己，就会逐步促成你生活中的积极转变！`,
          referenceChatHistory: true,
          temperature: 1,
          noCounselorPreFix: true
        })
      }
    }
  }
}