import { MoerWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { MoerNode } from './type'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { LLMNode } from './llm'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { ObjectUtil } from '../../../../../lib/object'
import { ExtractAddressSlots } from '../helper/extracAddress'
import { ExtractAddressSlotsPrompt } from '../../../prompt/moer/extractAddressSlots'
import { MessageSender } from '../../message/message_send'
import { FreeTalk } from '../../agent/freetalk'
import { DataService } from '../../../getter/getData'
import logger from '../../../../../model/logger/logger'
import { HumanTransfer, HumanTransferType } from '../../human_transfer/human_transfer'
import { sleep } from '../../../../../lib/schedule/schedule'
import { IWecomMsgType } from '../../../../../lib/juzi/type'


/**
 * 售后节点，询问客户 地址等信息
 */
export class PostSaleNode extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    if (ChatStateStore.get(state.chat_id).state.is_complete_post_sale) {
      return await FreeTalk.invoke(state)
    }

    // 检查客户的槽位信息（address, phone, name）
    const customerSlots = await this.getCustomerSlots(state)

    if (customerSlots.address) {
      // 如果客户已经提供了地址、姓名，进行售后处理
      await this.completePostSale(state)
      await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.ConfirmedAddress, 'onlyNotify')
      return MoerNode.FreeTalk
    } else {
      // 如果缺少地址、电话或姓名，请求客户提供
      return await this.requestCustomerInfo(state, customerSlots)
    }
  }

  // 获取客户的槽位信息
  private static async getCustomerSlots(state: IWorkflowState) {
    const chat_id = state.chat_id
    const chatHistory = await ChatHistoryService.getUserMessages(state.chat_id, 5)

    const prompt = await ExtractAddressSlotsPrompt.format(chatHistory.join('\n'))

    const extractedSlots = await ExtractAddressSlots.extract(prompt, ExtractAddressSlotsPrompt.schema, {
      chat_id,
      user_id: state.user_id,
      round_id: state.round_id,
    })

    const prevSlots = ChatStateStore.get(chat_id).userSlots
    let currentSlots = prevSlots

    if (extractedSlots) {
      Object.keys(extractedSlots).forEach((key) => {
        if (!extractedSlots[key]) {
          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
          delete extractedSlots[key]
        }
      })

      currentSlots = ObjectUtil.merge(prevSlots, extractedSlots)
      ChatStateStore.update(chat_id, { userSlots: currentSlots })
    }

    logger.log({ chat_id: state.chat_id }, '提取地址信息', JSON.stringify(currentSlots, null, 4))

    return currentSlots
  }

  // 处理完成售后流程
  private static async completePostSale(state: IWorkflowState) {
    ChatStateStore.update(state.chat_id, {
      state: {
        is_complete_post_sale: true
      }
    })

    const systemStartTime = await DataService.getSystemCourseStartTime(state.chat_id)

    await sleep(3000)

    if (systemStartTime) {
      await MessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: `好的，我们通知仓库寄哈，咱们的系统班是 ${systemStartTime} 开课，上课之前会安排新的助教老师来添加您哈。这是我们课表`
      })
    } else {
      await MessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: '好的，我们通知仓库寄哈，上课之前会安排新的助教老师来添加您哈。这是我们课表'
      })
    }
    await sleep(5000)

    await MessageSender.sendById({
      user_id: state.user_id,
      chat_id: state.chat_id,
      ai_msg: '[系统班课表]',
      send_msg: {
        type: IWecomMsgType.Image,
        url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E7%B3%BB%E7%BB%9F%E7%8F%AD%E8%AF%BE%E7%A8%8B%E8%A1%A8.png'
      }
    })

    await sleep(8000)

    await MessageSender.sendById({
      user_id: state.user_id,
      chat_id: state.chat_id,
      ai_msg: `咱们剩下2项福利：
1.【墨尔冥想APP会员季卡】已经自动发放到您的账号（时间已经加好啦），你已经可以在 APP 听会员专享音频啦！
2. 21天系统课笔记：系统班班班加到咱们之后会给咱们发放哈。`,
    })

    await sleep(8000)

    await MessageSender.sendById({
      user_id: state.user_id,
      chat_id: state.chat_id,
      ai_msg: '[查看会员指引]',
      send_msg: {
        type: IWecomMsgType.Image,
        url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E5%AD%A3%E5%8D%A1%E4%BC%9A%E5%91%98%E6%8C%87%E5%BC%95.png'
      }
    })
  }

  // 请求客户提供必要的售后信息
  private static async requestCustomerInfo(state: IWorkflowState, customerSlots: any) {
    const missingInfo: string[] = []

    if (!customerSlots.address) {
      missingInfo.push('地址')
      // 生成提示信息，要求客户提供缺失的槽位信息
      await LLMNode.invoke({
        state,
        dynamicPrompt: `为了给客户发货，还需要补充 ${missingInfo.join('、')} 信息，请进行询问。例如："咱这边地址是多少呢"`,
        useRAG: true,
        noUserSlots: true,
      })
      return MoerNode.PostSale
    } else {
      return await FreeTalk.invoke(state)
    }
  }
}