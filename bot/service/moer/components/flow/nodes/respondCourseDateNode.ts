import { <PERSON>r<PERSON>orkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { MoerNode } from './type'
import { LLMNode } from './llm'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { DataService } from '../../../getter/getData'
import { ChatHistoryService } from '../../chat_history/chat_history'
import logger from '../../../../../model/logger/logger'
import { ChatDB } from '../../../database/chat'
import { FreeTalk } from '../../agent/freetalk'
import { MoerNodeMap } from '../nodes'
import { IScheduleTime } from '../../schedule/creat_schedule_task'

/**
 * 我现在需要先找到当下时间，判断客户询问的课程是什么时间上，然后通过prompt把回答包装，给到客户
 */
export class RespondCourseDateNode extends Moer<PERSON>orkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    // 先找到当下时间，判断客户询问的课程是什么时间上，然后通过prompt把回答包装，给到客户
    const chat = await ChatDB.getById(state.chat_id)
    if (!chat) {
      return FreeTalk.invoke(state)
    }
    if (!chat.course_no) {
      logger.warn({ chat_id: state.chat_id, message: '客户课程暂未入库' })
      return FreeTalk.invoke(state)
    }

    const courseNo =  chat.course_no
    const courseDate = await DataService.getCourseStartTime(state.chat_id)
    const currentTime = await DataService.getCurrentTime(state.chat_id)
    const systemStartTime = await DataService.getSystemCourseStartTime(state.chat_id)
    const currentDate = new Date()

    // 课程周开始时间
    const courseWeekStart = new Date(courseDate)
    courseWeekStart.setHours(0, 0, 0, 0)

    // 计算班会开营时间（courseDate前一天的晚上八点）
    const campStartDate = new Date(courseDate)
    campStartDate.setDate(campStartDate.getDate() - 1)
    campStartDate.setHours(20, 0, 0, 0)

    // 计算加播课时间（courseDate后四天晚上八点）
    const addOnCourseDate = new Date(courseDate)
    addOnCourseDate.setDate(addOnCourseDate.getDate() + 3)
    addOnCourseDate.setHours(20, 0, 0, 0)

    // 使用 currentTime.is_course_week 判断是否在课程周
    const isInCourseWeek = currentTime.is_course_week ? '是' : '非'

    // 构建上下文信息
    const contextInfo = {
      currentDateCourseNo : courseNo,
      currentDate: currentDate.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
      campStartDate: campStartDate.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
      courseWeekStart: courseWeekStart.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
      addOnCourseDate : addOnCourseDate.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
      isInCourseWeek: isInCourseWeek,
      currentDay: currentTime.day,
      currentTime: currentTime.time,
      courseSchedule: [
        { day: -1, name:'开营班会', time: '20:00-20:20', content: '本次课程的授课导师介绍,学员将学习和收获的内容,上课地点和如何提高学习效果；上课方式是在微信社群直播' },
        { day: 1, name:'第一节课', time: '20:00-21:45', content: '情绪减压，解析情绪和睡眠问题的成因和处理方式，带练沉浸式秒睡；上课方式是在微信小程序或者墨尔app直播' },
        { day: 2, name:'第二节课', time: '20:00-21:15', content: '财富果园财富唤醒；上课方式是在微信小程序或者墨尔app直播' },
        { day: 3, name:'第三节课', time: '20:00-22:00', content: '效能提升，带练【红靴子】冥想；上课方式是在微信小程序或者墨尔app直播' },
        { day: 4, name:'加播课', time: '20:00-22:45', content: '成事显化，带练【蓝鹰预演】冥想，提升事业的成功率；上课方式是在微信小程序或者墨尔app直播' }
      ]
    }
    logger.trace(state.chat_id, '询问课程时间时的背景：', contextInfo)
    let dynamicPrompt = `当前情况：
- 现在是 ${contextInfo.currentDate}
- 现在是第 ${contextInfo.currentDateCourseNo} 期课程
- 班会开营时间是 ${contextInfo.campStartDate}
- 加播课的时间是 ${contextInfo.addOnCourseDate}
- 课程周开始时间是 ${contextInfo.courseWeekStart}
- 当前${contextInfo.isInCourseWeek}课程周第 ${contextInfo.currentDay} 天，时间是 ${contextInfo.currentTime}

课程安排：
${contextInfo.courseSchedule.map((course) =>
    `- 第${course.day}天 ${course.name}, 上课时间:${course.time}, 上课内容: ${course.content}`
  ).join('\n')}

请严格按照以下规则回答客户的问题：

1. 特殊时间段处理规则：
   - 在课程周第3天20:00之后，根据最近对话判断客户询问的是需要购买的后续课程还是当下的课程：
     a. 如果无法确认，尝试识别客户可能指代的课程，并向客户确认，如："您说的是系统班吗？系统班是一个21天的课程，每天上课时间是晚上八点，每天的直播时间大概在60-90分钟，因为每天都有冥想的带练和讲解哦"
     b. 如果客户询问系统班的开课时间，21天系统班的开课时间：${systemStartTime}，如果时间来不及话可以申请延期
     c. 如果是需要购买的后续课程相关问题，通过以下系统班背景信息回答："系统班是一个21天的课程，每天上课时间是晚上八点，每天的直播时间大概在60-90分钟，因为每天都有冥想的带练和讲解哦"
     d. 如果不是需要购买的后续课程相关问题，继续按照下面的常规课程回答规则处理

2. 参考最近对话确认客户提问内容：
   - 检查客户提问是否与课程安排中的课程名称或内容匹配
   - 如果不匹配，尝试识别客户可能指代的课程，并向客户确认，如："您说的是沉浸式秒睡吗？这节课已经上过啦/在xxx时候开课哦。"

3. 时间判断规则：
   - 对于加播课的回答必须严格遵循以下规则：
     a. 在非课程周第一天00:00到课程周第四天14:00期间，不可以告知客户加播课的信息，必须回答："咱们是周日开营，周一到周三是直播，周四社群复习学习，总共五天哈。"
     b. 在课程周第四天9:00到课程周第四天14:00期间，可以回答：“咱们今晚可能安排蓝鹰预演哦，一起期待下吧”
     c. 在课程周第四天14:00到课程周第四天20:00期间，才能回答："咱们是周日开营，周一到周三是直播，周四社群复习学习，总共五天哈,不过老师已经决定为大家加播一节课啦，时间是今天晚上20:00"
   - 其他课程按照正常时间进行回答

4. 回答格式要求：
   - 对未来课程：友好告知具体上课时间，如"这节课在明天晚上8点开始哦"
   - 对已结束课程：提醒错过并告知下一节课信息；如果是最后一节课，鼓励复习已学内容
   - 对正在进行的课程：告知客户正在上课和上课内容，并重点鼓励客户进入直播间观看
   - 保持回答简洁、准确，带有适度的鼓励性
   - 不要添加"如有问题随时告诉我"等客套话

5. 特别注意：
   - 开营班会在课程周开始前一天晚上进行，即课程周第-1天，比如课程周的第一天是11月4日，开营班会的时间就是在11月3日晚上20:00-20:20。
   - 开营班会会在社群进行图文介绍，不进行直播；只需要提醒客户关注社群即可
   - 加播课信息的展示必须严格遵循时间判断规则
   - 入门营直播课程都有回放，但是仍建议客户尽量参与直播，表明这样效果更好
   - 回答要准确反映当前时间点的情况
   - 入门营课程不支持调整到下期，如果客户没时间上直播，就引导其观看回放

请基于以上规则，结合客户最近的聊天记录，给出准确的回答：`

    if ((currentTime.post_course_week != undefined && currentTime.post_course_week >= 1) || this.isAfterDay4Course(currentTime)) {
      dynamicPrompt = await RespondCourseDateNode.getAfterCourseDynamicPrompt(state.chat_id)
    }
    await LLMNode.invoke({
      state,
      dynamicPrompt: dynamicPrompt,
      useRAG: true,
      noUserSlots: true,
    })

    const nextNode = ChatStateStore.get(state.chat_id).nextStage as MoerNode
    if ([MoerNode.Sales].includes(nextNode)) {
      const node = MoerNodeMap.get(nextNode)
      if (node) {
        return await node.invoke(state)
      }
    }

    return ChatStateStore.get(state.chat_id).nextStage as MoerNode
  }

  private static async getAfterCourseDynamicPrompt(chat_id: string) {
    const systemStartTime = await DataService.getSystemCourseStartTime(chat_id)
    return `当前情况：
5天冥想入门营已经结束，接下来是引导客户加入21天系统课精进学习。
21天系统课背景信息:
- 21天系统课是一个21天的课程，每天上课时间是晚上八点，每天的直播时间大概在60-90分钟，因为每天都有冥想的带练和讲解哦
- 21天系统班的开课时间：${systemStartTime}，如果时间来不及话可以申请延期
- 系统班是线上课程，在墨尔冥想的专属学习平台上进行的，课程是直播+智能直播结合的形式，每周有4节智能直播和1节直播答疑课，灵活安排时间学习
- 21天系统班里的课程是永久回放的，不用担心错过内容`
  }

  private static isAfterDay4Course(currentTime: IScheduleTime) {
    return currentTime.day >= 4 && currentTime.time >= '22:00' && (currentTime.is_course_week || currentTime.post_course_week)
  }
}