import { IWorkflowState } from '../flow'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { BaseMessage, HumanMessage, SystemMessage } from '@langchain/core/messages'
import { ChatInterruptHandler } from '../../message/interrupt_handler'
import { RegexHelper } from '../../../../../lib/regex/regex'
import { Config } from '../../../../../config/config'
import { MessageSender } from '../../message/message_send'
import { sleep } from '../../../../../lib/schedule/schedule'
import { ChatDB } from '../../../database/chat'
import logger from '../../../../../model/logger/logger'
import removeMarkdown from 'markdown-to-text'
import { ContextBuilder } from '../../agent/context'
import { ISendMedia } from '../../schedule/type'
import { IWecomMsgType } from '../../../../../lib/juzi/type'

interface IStreamReplyParams {
    chat_id: string
    user_id: string
    text: string
    interruptHandler?: ChatInterruptHandler
    lineHandler: (line: string, isFirstSentence: boolean) => Promise<string>
    noSplit?: boolean // 是否分句输出
}

export interface LLMInvokeParam {
    state: IWorkflowState

    customPrompt?: string // 自定义 Prompt 直接覆盖 SystemPrompt
    dynamicPrompt?: string // 作为 SystemPrompt 的 Task 部分进行插入
    customReplyContent?: string // 自定义内容，直接覆盖回复内容

    referenceChatHistory?: boolean

    recallMemory?: boolean // 是否做记忆召回
    useRAG?: boolean // 是否使用 RAG， 只用明确标记为 true，才调用

    notCheckRepeat?: boolean
    noTypingWaiting?: boolean // 对于长文本，取消模拟打字的等待时间
    promptName?: string // 用于记录当前的 prompt 名称
    regenerate?: boolean // 当输出重复导致所有输出被舍弃的时候，是否要重新生成文本
    noSplit?: boolean // 是否分割文本进行输出
    noInterrupt?: boolean // 不进行打断

    model?: string // 模型
    temperature?: number // 温度

    chatHistoryRounds?: number // 聊天记录轮数
    noCheckHumanInvolved?: boolean // 是否检查人工介入
    noCounselorPreFix?: boolean // 是否在聊天记录加入前置的 麦子老师:
    noStagePrompt?: boolean // 不添加 阶段 Prompt
    noUserSlots?: boolean // 不带客户槽位
    postReplyCallBack?: () => Promise<void> // 消息发送结束之后的回调
}


export class LLMNode {
  /**
     * 调用 LLM， 这里只是基础 销售的人设，可以通过 dynamicPrompt 来动态添加提示
     * @param param
     */
  public static async invoke(param: LLMInvokeParam) {
    logger.debug({ chat_id: param.state.chat_id }, 'enter LLMNode', JSON.stringify(param, null, 2)) // 输出进入节点的信息

    // 如果当前有新消息，把当前回复丢弃掉
    if (param.state.interruptHandler && !param.noInterrupt) { // 调用模型前检查一下
      await param.state.interruptHandler.interruptCheck()
    }

    if ((param.state.userMessage !== '' && param.customPrompt === undefined) || param.promptName === 'sales') {
      // 在开始回复前，检查 userMessage 是否为最后一句，如果不是，且后面一条是AI 回复，挪到 chat_history 底部
      await this.checkIsUserMessageLastMessageAndMoveToBottom(param.state.chat_id, param.state.userMessage)
    }

    const res = await this.llmReply(param, new LLM({
      meta: {
        chat_id: param.state.chat_id,
        round_id: param.state.round_id,
        promptName: param.promptName,
        dynamicPrompt: param.dynamicPrompt,
      },
      model: param.model,
      temperature: param.temperature,
    }))

    if (!res.reply) {
      logger.warn({ chat_id: param.state.chat_id }, `信息：“${param.state.userMessage} ” 没有回复`)
    }

    if (param.regenerate && !res.reply && !res.repeatedSentence) { // 空回复 + 重复的是哪句话
      if (param.dynamicPrompt) {
        param.dynamicPrompt += `**特别注意：不要重复回复"${res.repeatedSentence}"**`
      } else if (param.customPrompt) {
        param.customPrompt += `**特别注意：不要重复回复"${res.repeatedSentence}"**`
      }
      param.referenceChatHistory = true // 设置为任务遵循模式

      const regeneratedRes = await this.llmReply(param, new LLM({
        meta: {
          chat_id: param.state.chat_id,
          round_id: param.state.round_id,
          promptName: param.promptName,
          dynamicPrompt: param.dynamicPrompt,
        },
        model: param.model,
        temperature: param.temperature,
      }))

      return regeneratedRes.reply
    }

    if (param.postReplyCallBack) {
      await param.postReplyCallBack()
    }

    return res.reply
  }

  public static async llmReply(param: LLMInvokeParam, llm: LLM) {
    let replyContent: string
    if (param.customReplyContent != null) {
      replyContent = param.customReplyContent
    } else {
      replyContent = await this.getReplyContent(param, llm)
    }


    // 注意重复的时候，一次重复，丢弃掉剩下所有的回复
    let hasRepeated = false
    let repeatedSentence = ''

    const reply = await this.streamReply({
      chat_id: param.state.chat_id,
      user_id: param.state.user_id,
      noSplit: param.noSplit,
      text: replyContent,
      lineHandler: async (line: string, isFirstSentence) => {
        // 如果当前有新消息，把当前回复丢弃掉
        if (param.state.interruptHandler && !param.noInterrupt) {
          await param.state.interruptHandler.interruptCheck()
        }

        if (!param.noCheckHumanInvolved && await ChatDB.isHumanInvolvement(param.state.chat_id)) {
          logger.trace({ chat_id: param.state.chat_id }, '人工介入')
          return ''
        }

        // 假设人的平均打字速度为每秒 3 个字符
        const typingSpeed = 1.5
        // 计算当前消息的字数
        const charCount = line.length

        // 计算需要等待的时间 (毫秒)
        const delayTime = Math.floor(charCount / typingSpeed * 1000)

        if (!isFirstSentence && !Config.setting.localTest && !param.noTypingWaiting) {
          await sleep(delayTime)
        }

        if (!param.noCheckHumanInvolved && await ChatDB.isHumanInvolvement(param.state.chat_id)) {
          logger.trace({ chat_id: param.state.chat_id }, '人工介入')
          return ''
        }

        const isRepeated = await ChatHistoryService.isRepeatedMsg(param.state.chat_id, line)
        if (!param.notCheckRepeat && isRepeated) {
          hasRepeated = true
          repeatedSentence = line
          logger.warn({ chat_id: param.state.chat_id }, `重复回复已丢弃: ${line}`)
          return ''
        }

        // 发送前检查打断
        if (param.state.interruptHandler && !param.noInterrupt) {
          await param.state.interruptHandler.interruptCheck()
        }

        // 检查下是否有文件发送标记，如果有的话，把要发送的内容 split，然后分别发送
        await LLM.sendMsg(line, param.state.chat_id, param.state.user_id, param.state.round_id, param.noSplit)

        return line
      }
    })

    return {
      reply,
      repeatedSentence
    }
  }

  /**
     * 流式回复，将输入流分割成句子，并逐句处理
     * @param params
     */
  public static async streamReply(params: IStreamReplyParams): Promise<string> {
    let reply = ''
    let isFirstSentence = true

    // 预处理一下，开头结尾带 “” 直接移除掉
    if ((params.text.startsWith('“') && params.text.endsWith('”')) || (params.text.startsWith('"') && params.text.endsWith('"'))) {
      params.text = params.text.substring(1, params.text.length - 1)
    }

    // 如果 noSplit，则不按照句号/换行分割，直接处理为一条回复
    if (params.noSplit) {
      const processedSentence = await this.sentencePostProcess(params.text, params.chat_id)

      await params.lineHandler(processedSentence, isFirstSentence)
      reply = processedSentence
      return reply
    }

    // 正常分句模式
    const sentences = this.splitSentence(params.text)
    for (const rawSentence of sentences) {
      // 打断检查
      if (params.interruptHandler) {
        await params.interruptHandler.interruptCheck()
      }

      // 对句子做去 Markdown、去特殊符号、Emoji 去重、检查是否中文等处理
      let processedSentence = await this.sentencePostProcess(rawSentence, params.chat_id)

      if (processedSentence.trim().length === 0) { // 防止空文本发送
        continue
      }

      // 移除 '随时'，'想聊聊' 分句
      // 1. 将 sentence 按 ~ 分割
      const sentenceParts = processedSentence.split(/(~|～)/)

      // 2. 过滤掉包含 '随时'，'想聊聊' 的最后一个分句
      const keywords = ['随时找', '随时聊', '想聊聊']  // 你可以根据需要添加更多关键词
      if (sentenceParts.length > 0 && keywords.some((keyword) => sentenceParts[sentenceParts.length - 1].includes(keyword))) {
        logger.warn({ chat_id: params.chat_id }, `包含“随时”分句已丢弃: ${processedSentence}`)
        sentenceParts.pop()
      }

      processedSentence = sentenceParts.join('').trim()

      // 检查是否人工介入
      if (await ChatDB.isHumanInvolvement(params.chat_id)) {
        logger.trace({ chat_id: params.chat_id }, '人工介入')
        // 如果人工介入，可以选择直接return或continue
        return reply  // 假设直接返回，不再继续
      }

      // 检查重复回复
      const isRepeated = await ChatHistoryService.isRepeatedMsg(params.chat_id, processedSentence)
      if (isRepeated) {
        logger.warn({ chat_id: params.chat_id }, `重复回复已丢弃: ${processedSentence}`)
        continue
      }

      // 检查句子时间场景是否正确
      if (!LLMNode.checkTimeScene(processedSentence)) {
        logger.warn({ chat_id: params.chat_id }, `当前回复时间场景错误，已丢弃: ${processedSentence}`)
        continue
      }

      // 回调发送句子
      const sent = await params.lineHandler(processedSentence, isFirstSentence)
      if (sent) {
        reply += `${sent}\n`
      }

      // 只在第一句后将 isFirstSentence 置为 false
      if (isFirstSentence) {
        isFirstSentence = false
      }
    }
    await LLMNode.sendEmoticon(reply, params.chat_id, params.user_id)

    return reply.trim()
  }

  private static extractSentence(text: string): [string, string] {
    const index = text.search(/[!?~。！？\n]/)
    if (index !== -1) {
      return [text.substring(0, index + 1).trim(), text.substring(index + 1)]
    }
    return [text.trim(), '']
  }

  public static splitSentence(text: string) {
    // 1. 先按照序号分割，例如 "1. "、"2. " 等
    const regex = /\d+\.\s[^]*?(?=\d+\.\s|$)/g

    // 使用 match 方法提取所有匹配的段落
    const matchedSections = text.match(regex)

    // 清理分割结果：去除首尾空白并过滤空字符串
    const sections = matchedSections ? matchedSections.map((section) => section.trim()) : []

    let splitSentences: string[]
    if (sections.length > 1) {
      // 取出第一个序号分句之前的内容
      const preFirstSection = text.split(sections[0])[0].trim()
      if (preFirstSection) {
        splitSentences = [preFirstSection, ...sections]
      } else {
        splitSentences = sections
      }
    } else {
      // 2. 按标点分割成数组
      splitSentences = text
        .split(/(?<=[?!。！？\n])(?![”"'])/)
    }

    const rawSentences =  splitSentences.map((s) => s.trim())
      .filter(Boolean)

    // 2. 如果句子数 < 5，原样返回
    if (rawSentences.length < 5) {
      return rawSentences
    }

    // 3. 分句数 >=5 时，两两合并
    const combined: string[] = []
    for (let i = 0; i < rawSentences.length; i += 2) {
      // 取当前句子
      let merged = rawSentences[i]
      // 如果下一句存在，就合并
      if (rawSentences[i + 1]) {
        merged += ` ${rawSentences[i + 1]}`
      }
      combined.push(merged)
    }

    return combined
  }

  public static splitIntoSentencesWithMaxSentences(text: string, maxSentences = 3): string[] {
    const splitedSentence = this.splitSentence(text)
    if (splitedSentence.length <= maxSentences) {
      return splitedSentence
    }
    const refineSentences:string[] = []
    const howManySentenceMergeOne = splitedSentence.length / maxSentences + (splitedSentence.length % maxSentences > 0 ? 1 : 0)
    for (let i = 0; i < splitedSentence.length; i += howManySentenceMergeOne) {
      refineSentences.push(splitedSentence.slice(i, i + howManySentenceMergeOne).join(' '))
    }
    return refineSentences
  }

  private static async sentencePostProcess(sentence: string, chat_id: string) : Promise<string> {
    sentence = removeMarkdown(sentence)
    sentence = sentence.replaceAll(`${Config.setting.BOT_NAME}：`, '').replaceAll('**', '').replaceAll('客户：', '').replace('。', ' ').replaceAll(/\[\[[^\]]*]]/g, '').trim()
    const isMajorityChinese = RegexHelper.isMajorityChinese(sentence)
    if (!isMajorityChinese) {
      return '' // 直接丢弃输出
    }

    const emoji = RegexHelper.extractEmoji(sentence)
    if (emoji) {
      let botMessages = await ChatHistoryService.getBotMessages(chat_id)
      botMessages = botMessages.slice(-10)
      if (botMessages.some((msg) => msg.includes(emoji))) {
        sentence = sentence.replaceAll(emoji, '') // emoji 去重
      }
    }

    return sentence.trim()
  }

  private static async getReplyStream(param: LLMInvokeParam, llm: LLM) {
    if (!param.chatHistoryRounds && param.chatHistoryRounds !== 0) {
      param.chatHistoryRounds = 3
    }

    let chatHistory = await ChatHistoryService.getLLMChatHistory(param.state.chat_id, param.chatHistoryRounds)
    if (chatHistory.length > 20) {
      // 限制下上下文长度
      chatHistory = chatHistory.slice(-20)
    }

    const prompt = await ContextBuilder.build(param, chatHistory)

    return await llm.stream(this.buildMessages(prompt, chatHistory, param.referenceChatHistory, param.noCounselorPreFix))
  }

  private static buildMessages(prompt: string, chatHistory: BaseMessage[], referenceChatHistory?: boolean, noCounselorPreFix?: boolean): BaseMessage[] {
    const baseMessage = [new SystemMessage(prompt)]

    if (referenceChatHistory) {
      if (noCounselorPreFix) {
        return [...baseMessage]
      }
      return [...baseMessage, new HumanMessage(`${Config.setting.BOT_NAME}: `)]
    }

    return [...baseMessage, ...chatHistory]
  }

  public static async getReplyContent(param: LLMInvokeParam, llm: LLM) {
    // 确保 chatHistoryRounds 默认值
    if (param.chatHistoryRounds === undefined || param.chatHistoryRounds === null) {
      param.chatHistoryRounds = 3
    }

    let chatHistory = await ChatHistoryService.getLLMChatHistory(param.state.chat_id, param.chatHistoryRounds)
    if (chatHistory.length > 20) {
      // 限制一下上下文长度
      chatHistory = chatHistory.slice(-20)
    }

    const prompt = await ContextBuilder.build(param, chatHistory)

    const messages = this.buildMessages(
      prompt,
      chatHistory,
      param.referenceChatHistory,
      param.noCounselorPreFix
    )

    return await llm.predictMessage(messages)
  }

  private static async sendEmoticon(message: string, chat_id: string, user_id: string) {
    const imageSearchResult = LLMNode.getEmoticon(message)
    if (!imageSearchResult) {
      return
    }
    const isRepeated = await ChatHistoryService.isRepeatedMsg(chat_id, imageSearchResult?.description)
    if (isRepeated) {
      return
    }
    await sleep(2000)

    await MessageSender.sendById({
      chat_id: chat_id,
      user_id: user_id,
      ai_msg: `[${imageSearchResult.description}]`,
      send_msg: imageSearchResult.msg
    })

  }

  private static getEmoticon(message: string) :ISendMedia | undefined {
    if (message.includes('晚安')) {
      return {
        description: '唐宁老师晚安表情包',
        msg: {
          type: IWecomMsgType.Emoticon,
          imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/emoji/%e6%99%9a%e5%ae%89%e8%a1%a8%e6%83%85%e5%8c%85.gif'
        }
      }
    }
    if (message.includes('早安') || message.includes('早上好')) {
      return {
        description: '唐宁老师早安表情包',
        msg: {
          type: IWecomMsgType.Emoticon,
          imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/emoji/%e6%97%a9%e4%b8%8a%e5%a5%bd%e8%a1%a8%e6%83%85%e5%8c%85.gif'
        }
      }
    }
    if (message.includes('加油')) {
      return {
        description: '唐宁老师加油表情包',
        msg: {
          type: IWecomMsgType.Emoticon,
          imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/emoji/%e5%8a%a0%e6%b2%b9%e8%a1%a8%e6%83%85%e5%8c%85.gif'
        }
      }
    }
    if (message.includes('抱抱')) {
      return {
        description: '抱抱表情包',
        msg: {
          type: IWecomMsgType.Emoticon,
          imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/emoji/%e6%8a%b1%e6%8a%b1%e8%a1%a8%e6%83%85%e5%8c%85.gif'
        }
      }
    }
  }

  private static checkTimeScene(message: string) {
    const currentHour = new Date().getHours()
    if (currentHour >= 5 && currentHour <= 17) {
      return !message.includes('晚安')
    } else if (currentHour >= 17 || currentHour === 0)
    {
      return !message.includes('早安') || !message.includes('早上好')
    }

    return true
  }

  private static async checkIsUserMessageLastMessageAndMoveToBottom(chat_id: string, userMessage: string) {
    // 在开始回复前，检查 userMessage 是否为最后一句，如果不是，且后面一条是AI 回复，挪到 chat_history 底部
    const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chat_id)
    const lastMessage = chatHistory[chatHistory.length - 1]

    if (lastMessage.content === userMessage) {
      return
    }


    let userMessageIndex = -1
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].role === 'user' && chatHistory[i].content === userMessage) {
        userMessageIndex = i
        break
      }
    }

    if (userMessageIndex === -1) {
      return
    }

    if (userMessageIndex + 1 > chatHistory.length - 1) {
      return
    }

    const nextMessage = chatHistory[userMessageIndex + 1]

    if (nextMessage.role === 'assistant') {
      await ChatHistoryService.moveToEnd(chat_id, userMessage)
    }
  }
}