import logger from '../../../../model/logger/logger'
import { RedisCacheDB } from '../../../../model/redis/redis_cache'


export class InterruptError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'InterruptError'
  }
}

/**
 * 在有新的客户消息的时候增加版本号
 * AI 回复前进行校验
 */
export class ChatInterruptHandler {
  private readonly chat_id: string
  private readonly expectedVersion: number

  /**
   * 构造函数是私有的，请使用静态的 `create` 方法来实例化
   * @param chat_id - 目标聊天的 ID
   * @param initialVersion - 进程开始时捕获的“预期”版本号
   */
  private constructor(chat_id: string, initialVersion: number) {
    this.chat_id = chat_id
    this.expectedVersion = initialVersion
  }

  /**
   * 工厂方法：创建一个处理器实例，并自动获取当前的最新版本号作为预期版本。
   * 这是开始一个长流程前应该调用的方法。
   * @param chat_id - 目标聊天的 ID
   */
  public static async create(chat_id: string): Promise<ChatInterruptHandler> {
    const initialVersion = await this.getChatVersion(chat_id)
    return new ChatInterruptHandler(chat_id, initialVersion)
  }

  public async interruptCheck() {
    const currentVersion = await ChatInterruptHandler.getChatVersion(this.chat_id)
    if (currentVersion !== this.expectedVersion) {
      const msg = '当前客户有新消息，当前流程被打断'
      logger.trace({ chat_id: this.chat_id }, msg)
      throw new InterruptError(msg)
    }
  }

  /**
   * 检查版本是否已更改，返回一个布尔值，不抛出异常。
   */
  public async hasChanged(): Promise<boolean> {
    const currentVersion = await ChatInterruptHandler.getChatVersion(this.chat_id)
    return this.expectedVersion !== currentVersion
  }

  /**
   * 让指定聊天的版本号+1。
   * @param chat_id - 目标聊天的 ID
   */
  public static async incrementChatVersion(chat_id: string): Promise<number> {
    return await new RedisCacheDB(this.getChatVersionKey(chat_id)).incr()
  }

  private static async getChatVersion(chat_id: string) {
    const chatVersion = await new RedisCacheDB(this.getChatVersionKey(chat_id)).get()
    if (chatVersion !== null) {
      return parseInt(chatVersion, 10)
    } else {
      await new RedisCacheDB(this.getChatVersionKey(chat_id)).set(0)
      return 0
    }
  }

  private static getChatVersionKey(chat_id: string): string {
    return `chat:${chat_id}:version` // 使用冒号是 Redis key 命名的好习惯
  }
}