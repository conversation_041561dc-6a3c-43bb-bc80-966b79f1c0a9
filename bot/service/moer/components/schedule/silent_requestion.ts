import { DelayedTask } from '../../../../lib/schedule/delayed_task'
import { ChatInterruptHandler } from '../message/interrupt_handler'
import { ChatHistoryService } from '../chat_history/chat_history'
import logger from '../../../../model/logger/logger'

interface IReAskOptions {
  auto_retry?: boolean // 被新发送的客户消息打断后，是否进行自动重试，true 的话，直到没有新消息，后续会再次重试执行。默认为 false, 被打断后不再执行
  independent?: boolean // 如果为 true, 不将之前的任务取消。默认取消之前的任务
}

// function calculateCompressedTime(chat_id: string,  waiting_time: number): number {
//   const compressionFactor = TimeConfig.compressionDuration(chat_id) / TimeConfig.originalDuration
//   return waiting_time * compressionFactor
// }


/**
 * 追问任务
 * 用于在客户没有回复的情况下，自动进行追问
 * 注意：
 * 1. 每个对话都有独立的追问计时
 * 2. 每次添加新的追问，都要清除之前的追问任务，同时只能有一个追问任务在执行
 */
export class SilentReAsk {
  private static taskMap: Map<string, DelayedTask> = new Map<string, DelayedTask>()

  /**
   * 添加追问任务
   * @param chat_id
   * @param task
   * @param waiting_time
   * @param options
   */
  public static async schedule(chat_id: string, task: () => Promise<any>, waiting_time: number, options?: IReAskOptions): Promise<void> {
    if (this.taskMap.has(chat_id) && !(options?.independent)) {
      this.taskMap.get(chat_id)?.cancel() // 同一个 chat_id 下只能有一个延时任务
    }

    let newMessageChecker = await ChatInterruptHandler.create(chat_id)

    const updateChecker = async () => {
      newMessageChecker = await ChatInterruptHandler.create(chat_id)
    }

    const checkCondition = async () => {
      const noNewMessage = !(await newMessageChecker.hasChanged())

      if (noNewMessage) {
        return true
      } else {
        logger.trace({ chat_id }, '有新消息，任务被重新执行')
        return false
      }
    }

    const delayedTask = new DelayedTask(waiting_time, task, checkCondition, options?.auto_retry, updateChecker)

    await delayedTask.start()

    if (!(options?.independent)) {
      this.taskMap.set(chat_id, delayedTask) // 独立任务不进入 taskMap
    }
  }
}