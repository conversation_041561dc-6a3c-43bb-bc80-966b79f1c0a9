import logger from '../../../../model/logger/logger'
import { EventTracker, IEventType } from '../../../../model/logger/data_driven'
import { IWorkflowState } from '../flow/flow'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { MetaActionStage } from '../meta_action/meta_action_router'
import { ContextBuilder } from './context'
import { SalesNodeHelper } from '../flow/helper/salesNodeHelper'
import { getPrompt } from './prompt'

/**
 * MetaAction 类用于定义不同销售阶段的“元行为”。
 * 每个 metaActions 集合代表一个阶段，例如课中、课后、卖课等。
 * 元行为是对话策略的基本单元，用于指导 AI 在特定情境下如何回应客户。
 */
export class FreeThink {
  /**
   * 元行为集合 - 不同阶段：
   * - 主要目标：维持课堂专注，引导学习行为。
   * - 使用场景：客户在听课期间提出的问题或讨论。
   * - 填写规则：
   *   - key: 行为名称（中文，动词+名词组合，不超过9个字）
   *   - value: 行为描述，需简洁明了地说明该行为的应用场景与表达方式
   *   - 注意避免提供具体解决方案，只引导方向
   */
  // 元行为：进量，铺垫 --> 15轮 & 第三课前
  public static metaActionIntroduction: Record<string, string> = {
    '回答问题': '回答客户问题',
    '拒绝要求': '委婉拒绝客户的无理要求，包括但不限于：加私信，要照片，打语音电话，谈恋爱等',
    '表演真人': '当客户怀疑你是AI，机器人或智能体时，要巧妙用真人的方式化解，如：笑了，AI能这么智能...',
    '引导上课': '结合客户画像与客户行为引导其参与后续直播课程，并推荐直播间听课以获得最好效果，注意不要频繁引导',
    '建立信任': '当客户出现描述个人难处时，要做好一个倾听者，给对方提供一个舒适对话环境。帮助客户获得更好的对话体验',
    '建立连接': '用第一人称分享与客户类似的经历、冥想成长过程，拉近关系，增强亲切感',
    '表示理解': '当客户提到压力、焦虑、生活挑战时，使用情感共鸣语言接住客户表达，传递被理解和信任的感受，其他事实类问题不要共情',
    '鼓励客户': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '增强学习信心': '根据客户画像，提及其他相似身份客户（如宝妈、职场人）在学习过程中的真实反馈，增强客户的学习信心',
    '介绍课程老师': '唐宁老师的教学已惠及数千万学员，培育了600多位冥想老师。为让更多人接触冥想，她用720小时精心打磨了入门启蒙课程，受到广泛认可',
    '提醒完成小讲堂': '若客户未完成小讲堂，且历史对话没有提醒过，可以提醒客户完成小讲堂，并发送小讲堂链接，不要频繁提醒',
    // 新版破冰，铺垫
    '询问冥想经验': '判断客户是初学者、进阶者还是已有经验，可以通过提问明确客户冥想接触程度，如：之前有试过冥想吗？或咱们之前冥想感受怎么样呢？有什么想解决的问题不？',
    '询问学习目标': '通过提问探索客户为何对冥想感兴趣，期待解决什么问题（如情绪、睡眠、焦虑或财富等）',
    '询问当下困扰': '通过开放式问题引导客户表达最近的状态或挑战，便于后续匹配课程价值',
    '介绍入门营课程': '根据客户反馈引出“我们为不同风格的人准备了3类冥想：听觉型->沉浸式秒睡，视觉型->财富果园，体感型->红靴子”，帮助客户理解课程背后专业设计，对齐期待',
    '打消顾虑': '告知课程节奏轻松友好、适合不同节奏人群（不怕跟不上），帮助打消初学者顾虑',
    '引发好奇': '通过“有些人说冥想像能量觉醒的开关”式语言激起客户探索兴趣',
  }
  // 元行为：铺垫15轮 --> 第一课前
  public static metaActionAfterIntroduction: Record<string, string> = {
    '回答问题': '回答客户问题',
    '拒绝要求': '委婉拒绝客户的无理要求，包括但不限于：加私信，要照片，打语音电话，谈恋爱等',
    '表演真人': '当客户怀疑你是AI，机器人或智能体时，要巧妙用真人的方式化解，如：笑了，AI能这么智能...',
    '鼓励客户': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '建立信任': '当客户出现描述个人难处时，要做好一个倾听者，给对方提供一个舒适对话环境。帮助客户获得更好的对话体验',
    '引导目标与痛点': '若客户未明确表达目标或痛点，通过提问方式明确客户的真实需求，用于客户未表达明确学习目的或只泛泛说“想了解”时',
    '铺垫入门营课程': '铺垫入门营对其个人价值，提升客户对后续课程内容的期待和重视度，保证后续到课率和完课率。',
    '提醒完成小讲堂': '若客户未完成小讲堂，且历史对话没有提醒过，可以提醒客户完成小讲堂，并发送小讲堂链接，不要频繁提醒',
    '强调课程重要性': '引导客户关注第一节课的减压作用、放松体验，强调首课的重要性',
  }
  // 元行为：每节课课中
  public static metaActionDuringCourse: Record<string, string> = {
    '回答问题': '回答客户问题',
    '确认回答': '如果客户表示已经在直播间了，简单回复，不要说别的',
    '鼓励客户': '当客户积极学习课程内容时但对课程学习信心不够时，给信心',
    '建立信任': '当客户出现描述个人难处时，要做好一个倾听者，给对方提供一个舒适对话环境。帮助客户获得更好的对话体验',
    '引导进入直播间': '唐宁老师正在直播间上课哈，咱们先进去听课，有什么问题课后再说',
  }
  // 元行为：第一课前 --> 第三课卖课
  public static metaActionAfterCourse1: Record<string, string> = {
    '回答问题': '回答客户问题',
    '拒绝要求': '委婉拒绝客户的无理要求，包括但不限于：加私信，要照片，打语音电话，谈恋爱等',
    '表演真人': '当客户怀疑你是AI，机器人或智能体时，要巧妙用真人的方式化解，如：笑了，AI能这么智能...',
    '引导上课': '结合客户画像与客户行为引导其参与后续直播课程，并推荐直播间听课以获得最好效果，注意不要频繁引导',
    '明确冥想价值': '通过客户反馈的体验感受，判断是否已感受到冥想对其问题的帮助，用于客户表达体验感受、或提到练习后状态变化时',
    '询问练习感受': '结合客户行为，询问客户练习感受以便更深入的了解',
    '引导目标与痛点': '若客户未明确表达目标或痛点，通过提问方式明确客户的真实需求，用于客户未表达明确学习目的或只泛泛说“想了解”时',
    '推荐补课': '若客户行为中，客户未完成部分入门营课程，根据其目标推荐对应课程内容',
    '鼓励冥想坚持': '鼓励客户持续练习、建立冥想习惯，减轻焦虑或结果焦虑，用于客户担忧是否能坚持或对自己练习能力不自信时',
    '放大体验爽点': '强化客户喜欢的某种冥想风格、课中获得的积极体验等，用于客户主动提到喜欢课程中某部分时',
  }
  // 元行为：第三课卖课 --> 第四课结束
  public static metaActionAfterCourse3: Record<string, string> = {
    // 明确价值
    '回答问题': '若客户提到系统班，或者价格费用相关，要认真回答',
    '拒绝要求': '委婉拒绝客户的无理要求，包括但不限于：加私信，要照片，打语音电话，谈恋爱等',
    '表演真人': '当客户怀疑你是AI，机器人或智能体时，要巧妙用真人的方式化解，如：笑了，AI能这么智能...',
    '明确冥想价值': '通过客户反馈的体验感受，判断是否已感受到冥想对其问题的帮助，用于客户表达体验感受、或提到练习后状态变化时',
    '引导目标与痛点': '若客户未明确表达目标或痛点，通过提问方式明确客户的真实需求，用于客户未表达明确学习目的或只泛泛说“想了解”时',
    '推荐补课': '客户未完成任何入门营课程，而且不是意向客户，根据其目标推荐对应课程内容',
    '鼓励冥想坚持': '鼓励客户持续练习、建立冥想习惯，减轻焦虑或结果焦虑，用于客户担忧是否能坚持或对自己练习能力不自信时',
    '放大体验爽点': '善用扬长避短，当客户主动提到喜欢课程中某部分时，强化客户喜欢的某种冥想风格、课中获得的积极体验',
    '放大痛点后果': '结合客户画像中的痛点或客户经历，放大不解决当下困扰将继续带来的消极影响',
    '询问时间安排': '当客户表示忙或要做其他事情时，询问其具体的时间安排，比如是因为生活还是因为报了其他课程？',
    // 推荐系统班
    '转移注意': '当客户提到价格太贵时，将注意力从价格转移到冥想习惯的回报，并探索客户真正的顾虑点，如：其实很多学员反馈，通过课程养成的冥想习惯，帮助他们减少焦虑、提升工作效率，反而创造了更多价值。想问问您，如果费用问题可以解决的话，您最希望通过这个课程获得哪方面的改变呢？',
    '软化时间阻力': '当客户提到没时间上系统班时，告知客户系统班课程支持灵活学习、永久回放、不影响正常生活等优势',
    '介绍系统班价值': '结合客户痛点/目标/体验，放大课程卖点，如正念改善情绪、睡眠、突破限制性念头等，用于客户已表达价值认可时',
    '提供延期方案': '如果客户实在时间来不及参加本期系统班，可以先报名占住名额锁定权益，登记延期到下一期开课',
    '支持分期付款': '若客户有经济问题，可以告知我们支持分期付款',
    '传递稀缺性与紧迫感': '提示系统班程名额有限、优惠截止、本期特权不保留等策略性语言，用于推进临门一脚报名阶段',
    '询问下单卡点': '若客户下单卡点不明确或者回应模糊时，结合客户痛点或目标提出策略性问题（假设性提问）获取客户真正的顾虑，比如：您担心...是因为...吗？提问时就不要使用别的元行为，专注提问即可',
    '强调督导支持': '强调1对1指导、社群陪伴、老师答疑、班主任督课等内容，用于客户表达担心学不会、效果不稳时',
    '发送学员案例': '当客户怀疑课程效果时，或你需要说服客户购买时，引用真实学员解决痛点或实现提升的案例，增强课程效果的可信度',
    '发起报名邀约': '当客户表达认可度高、意愿强烈、卡点已清除时，明确地提出报名指引或邀请',
  }
  // 元行为：第四课结束 --> 上课周结束
  public static metaActionAfterCourse4: Record<string, string> = {
    // 明确价值
    '回答问题': '若客户提到系统班，或者价格费用相关，要认真回答',
    '拒绝要求': '委婉拒绝客户的无理要求，包括但不限于：加私信，要照片，打语音电话，谈恋爱等',
    '表演真人': '当客户怀疑你是AI，机器人或智能体时，要巧妙用真人的方式化解，如：笑了，AI能这么智能...',
    '询问目标': '如果在客户画像中不包含冥想目标时，可以通过提问方式明确客户的真实目标需求，比如：你想通过冥想实现什么呢',
    '询问痛点': '当不明确客户的痛点时，可以结合客户画像询问客户痛点，提问时就不要使用别的元行为，专注提问即可',
    '询问意图': '若客户发言较为模糊，你需要发起询问让客户澄清意图',
    '推荐补课': '客户未完成任何入门营课程，而且不是意向客户，根据其目标推荐对应课程内容',
    '询问下单卡点': '若客户下单卡点不明确或者回应模糊时，结合客户痛点或目标提出策略性问题（假设性提问）获取客户真正的顾虑，比如：您担心...是因为...吗？提问时就不要使用别的元行为，专注提问即可',
    '鼓励坚持行为': '当客户担忧是否能坚持或对自己练习能力不自信时，鼓励客户持续练习、建立冥想习惯，减轻焦虑或结果焦虑',
    '匹配冥想风格': '根据客户画像中的偏好匹配财富果园、沉浸秒睡或红靴子，用于客户表达某类冥想喜欢或不确定自己适合哪类时',
    '放大体验爽点': '善用扬长避短，当客户主动提到喜欢课程中某部分时，强化客户喜欢的某种冥想风格、课中获得的积极体验，说明在系统班中都可以更深入的体验',
    '放大痛点后果': '结合客户画像中的痛点或客户经历，放大不解决当下困扰将继续带来的消极影响，强化系统班作用',
    '明确冥想价值': '当客户觉得系统班没用，或者表示不了解冥想价值时，结合系统班中的具体模块与客户的反馈体验或痛点，一一对应说明系统班如何解决其核心问题，深化客户对冥想价值与系统班价值的理解',
    '软化时间阻力': '当客户提到没时间上系统班时，告知客户系统班课程支持灵活学习、永久回放、不影响正常生活等优势',
    '询问时间安排': '当客户表示忙或要做其他事情时，询问其具体的时间安排，比如是因为生活还是因为报了其他课程？',
    // 推荐系统班
    '对比课程区别': '说明体验课只为小白设计，涵盖三类风格冥想，帮助找到适合自己的练习方向，但是如果想深入学习感受还是要参加系统班',
    '介绍系统班权益': '说明系统班课程包含的赠品（坐垫与会员卡），扣除赠品，1000出头就可以开启学习',
    '介绍系统班价值': '结合客户痛点/目标/体验，放大课程卖点，如正念改善情绪、睡眠、突破限制性念头等，用于客户已表达价值认可时',
    '强调系统班优势': '结合客户痛点，介绍系统班中坐禅立禅卧禅/五大呼吸设计，如何解决“进不去状态、呼吸憋、体式疼”等问题，用于客户在意学习效果或反馈练习瓶颈时',
    '提供延期方案': '如果客户实在时间来不及参加本期系统班，可以先报名占住名额锁定权益，登记延期到下一期开课',
    '支持分期付款': '若客户有经济问题，可以告知我们支持分期付款',
    '提供保留名额': '如果客户一直犹豫不决，可以说明帮客户保留赠品名额到今晚',
    '传递稀缺性与紧迫感': '提示系统班程名额有限、优惠截止、本期特权不保留等策略性语言，用于推进临门一脚报名阶段',
    '解决客户异议': '当客户表达担心学不会，效果不稳，无法坚持或中途放弃时，强调系统班课程中1对1指导、周答疑和社群陪伴设计，班主任督课等内容，助力持续突破练习瓶颈',
    '发送学员案例': '当客户怀疑课程效果时，或你需要说服客户购买时，引用真实学员通过系统班解决痛点或实现提升的案例，增强课程效果的可信度',
    '发起报名邀约': '当客户表达认可度高、意愿强烈、卡点已清除时，明确地提出报名指引或邀请',
  }
  // 元行为：上课周结束 --> 买课/流失
  public static metaActionAfterCourseWeek: Record<string, string> = {
    '回答问题': '回答客户问题',
    '拒绝要求': '委婉拒绝客户的无理要求，包括但不限于：加私信，要照片，打语音电话，谈恋爱等',
    '表演真人': '当客户怀疑你是AI，机器人或智能体时，要巧妙用真人的方式化解，如：笑了，AI能这么智能...',
    '明确冥想价值': '通过客户反馈的体验感受，判断是否已感受到冥想对其问题的帮助，用于客户表达体验感受、或提到练习后状态变化时',
    '询问练习感受': '结合客户行为，询问客户练习感受以便更深入的了解',
    '引导目标与痛点': '若客户未明确表达目标或痛点，通过提问方式明确客户的真实需求，用于客户未表达明确学习目的或只泛泛说“想了解”时',
    '推荐补课': '若客户行为中，客户未完成部分入门营课程，根据其目标推荐对应课程内容',
    '鼓励冥想坚持': '鼓励客户持续练习、建立冥想习惯，减轻焦虑或结果焦虑，用于客户担忧是否能坚持或对自己练习能力不自信时',
    '放大体验爽点': '强化客户喜欢的某种冥想风格、课中获得的积极体验等，用于客户主动提到喜欢课程中某部分时',
    '发起报名邀约': '当客户表达认可度高、意愿强烈、卡点已清除时，明确地提出报名指引或邀请',
  }
  // 元行为：售后
  public static metaActionAfterPaid: Record<string, string> = {
    '回答问题': '回答客户问题',
    '鼓励客户': '当客户积极学习课程内容时但对课程学习信心不够时，加强对21天课程和学习冥想的信心',
    '建立连接': '用第一人称分享与客户类似的经历、冥想成长过程，拉近关系，增强亲切感',
    '共情回应': '当客户提到压力、焦虑、生活挑战时，使用情感共鸣语言接住客户表达，传递被理解和信任的感受',
    '增强学习信心': '根据客户画像，提及其他相似身份客户（如宝妈、职场人）在学习过程中的真实反馈，增强客户的学习信心',
    '鼓励坚持行为': '当客户担忧是否能坚持或对自己练习能力不自信时，鼓励客户持续练习、建立冥想习惯，减轻焦虑或结果焦虑',
    '询问原因': '在客户表达退费或者取消报名意愿时，询问客户退费的原因',
    '坚定挽留': '在客户表达退费或者取消报名意愿时，要坚定挽留，可以通过客户画像结合冥想作用扬长避短，求同存异，可适度反驳客户',
    '放大体验爽点': '善用扬长避短，当客户主动提到喜欢课程中某部分时，强化客户喜欢的某种冥想风格、课中获得的积极体验',
    '放大痛点后果': '结合客户画像中的痛点或客户经历，放大不解决当下困扰将继续带来的消极影响',
  }
  // 思考Prompt：最顶层的思想，不同阶段采用不同的思考内核，包括如何调度元行为，如何适配客户现状，如何引导策略输出等，要求尽量简洁，与业务解耦，给模型最大的发挥空间
  public static thinkPromptIntroduction = '参考对话历史、客户画像与客户行为，思考如何才能使用元行为推进对话、增强信任、加深了解、提高客户对课程的期待，铺垫到课重要性，请使用最合适的元行为，不超过1个'
  public static thinkPromptAfterIntroduction = '参考对话历史、客户画像与客户行为，思考如何才能使用元行为增强信任、加深了解、提高客户对课程的期待，请使用最合适的元行为，不超过2个，也可以只选1个'
  public static thinkPromptDuringCourse = '参考对话历史、客户画像与客户行为，思考如何才能使用元行为引导客户现在就去上课。请使用最合适的元行为，不超过1个'
  public static thinkPromptAfterCourse1 = '参考对话历史、客户画像与客户行为，思考如何才能使用元行为加深了解、渗透冥想价值、铺垫唐宁老师实力，请使用最合适的元行为，不超过2个，也可以只选1个'
  public static thinkPromptAfterCourse3 = '参考对话历史、客户画像与客户行为，思考如何才能使用元行为增强信任、渗透冥想价值，其中只给高意向客户介绍系统班，其他客户则不要提及；如果对话历史提到系统班，客户没有太大兴趣，避免再次提及！请使用最合适的元行为，不超过2个，也可以只选1个'
  public static thinkPromptAfterCourse4 = `参考对话历史、客户画像与客户行为，思考如何才能使用元行为挖掘顾虑、解决异议、深化价值理解，推进销售系统班进程。请使用最合适的元行为，不超过2个，也可以只选1个。善用提问与发送案例，一定不能放走客户，更不能把希望寄托于未来转化！请结合以下几点展开推理：
  1. 当前客户所处的销售阶段（如：需求挖掘、价值塑造、异议处理、成交推进等），当前应达成的阶段目标是什么？
  2. 回顾上轮动作及客户回应，是否达到了预期目标？客户的态度是积极、模糊、回避、还是抗拒？
  3. 客户最近几轮情绪或动机是否发生变化？当前策略是否仍匹配？是否需要在语气、节奏或行为风格上做出调整？
  4. 本轮是否存在行为跳跃、重复、节奏不当的问题？是否触发了过度灌输、反复提问、情绪压强过大的风险？
  5. 是否选择了比较软性的策略错误的寄希望于未来转化？而不是积极思考其他有效策略？
  6. 如存在偏差，请主动优化策略方向，调整元行为的选择，使其更符合销售节奏`
  public static thinkPromptAfterCourseWeek = '参考对话历史、客户画像与客户行为，思考如何才能使用元行为加深了解、渗透冥想价值、铺垫唐宁老师实力，请使用最合适的元行为，不超过2个，也可以只选1个'
  public static thinkPromptAfterPaid = '参考对话历史、客户画像与客户行为，思考如何才能使用元行为推进对话，请使用最合适的元行为，不超过2个，也可以只选1个。客户已经购买，无需继续推销，正常回应即可；若客户想退款，一定不能同意，想尽一切办法挽留'

  public static async invoke(state: IWorkflowState, metaActionStage: MetaActionStage) {
    const freeThinkPrompt = await getPrompt('free-think')
    const randomTemperature = Number((Math.random() * 0.3 + 0.7).toFixed(2))
    const customerBehavior = await ContextBuilder.getCustomerBehavior(state.chat_id)
    const customerPortrait = await ContextBuilder.getCustomerPortrait(state.chat_id)
    const dialogHistory = await SalesNodeHelper.getChatHistory(state.chat_id, 6, 18)
    const temporalInformation = await ContextBuilder.getTimeInformation(state.chat_id)
    await state.interruptHandler.interruptCheck()

    const output = await LLM.predict(
      freeThinkPrompt, {
        temperature: randomTemperature,
        responseJSON: true,
        meta: {
          promptName: 'free_think',
          chat_id: state.chat_id,
          round_id: state.round_id
        } }, {
        thinkPrompt: metaActionStage.thinkPrompt,
        metaActions: metaActionStage.metaActions,
        customerBehavior: customerBehavior,
        customerPortrait: customerPortrait,
        dialogHistory: dialogHistory,
        temporalInformation: temporalInformation,
      })

    let think: string = ''
    let action: string[] = []
    let strategy: string = '正常回复'
    let content: string = ''

    try {
      const parsedOutput = JSON.parse(output)
      think = parsedOutput.think
      action = parsedOutput.action
      strategy = parsedOutput.strategy
      content = parsedOutput.content
    } catch (error) {
      logger.error('FreeThink 解析 JSON 失败:', error)
    }

    EventTracker.track(state.chat_id, IEventType.FreeThink, { round_id: state.round_id, think: think, action: JSON.stringify(action), strategy: strategy, content: content })
    logger.debug({ chat_id: state.chat_id, round_id: state.round_id }, `think: ${think}\naction: ${JSON.stringify(action)}\nstrategy: ${strategy}`)
    return { think, action, strategy, content }
  }
}