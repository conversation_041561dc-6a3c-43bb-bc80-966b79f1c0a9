import { <PERSON>r<PERSON>orkFlowNode, trackInvoke } from '../flow/nodes/baseNode'
import { IWorkflowState } from '../flow/flow'
import { MoerNode } from '../flow/nodes/type'
import { LLMNode } from '../flow/nodes/llm'
import { ChatStateStore } from '../../storage/chat_state_store'
import { FreeThink } from './freethink'
import { PostSaleNode } from '../flow/nodes/postSaleNode'
import { MetaActionRouter } from '../meta_action/meta_action_router'

export class FreeTalk extends MoerWorkFlowNode {

  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const chat_id = state.chat_id
    if (ChatStateStore.get(chat_id).state.is_complete_payment && !ChatStateStore.get(chat_id).state.is_complete_post_sale) {
      return await PostSaleNode.invoke(state)
    }
    // 针对外部测试人员的降智版本
    const test_chat_ids = ['7881299599245568_1688855548631328']
    let action: string[]
    let strategy: string
    let metaActionStageInfo = ''

    if (test_chat_ids.includes(chat_id)) {
      action = ['']
      strategy = '跟客户开玩笑，完全无下限'
    } else {
      const metaActionStage = await MetaActionRouter.getThinkAndMetaActions(chat_id, state.round_id)
      const result = await FreeThink.invoke(state, metaActionStage)
      metaActionStageInfo = await MetaActionRouter.getGuidance(chat_id, result.strategy, result.action, state.round_id)
      action = result.action
      strategy = result.strategy
    }

    const actionInfo = await MetaActionRouter.handleAction(chat_id, state.round_id, action)

    await LLMNode.invoke({
      state,
      model: test_chat_ids.includes(chat_id) ? 'gpt-4.1-mini' : 'gpt-4.1',
      useRAG: true,
      recallMemory: true,
      chatHistoryRounds: 6,
      promptName: 'free_talk',
      noStagePrompt: true,
      dynamicPrompt: `无论客户最后说什么，参考上述信息回答完客户后，都要严格执行下面的策略，要求极度精简，点到为止
${strategy}${metaActionStageInfo}${actionInfo.additionInfo}`,
      postReplyCallBack: actionInfo.callback
    })

    return MoerNode.FreeTalk
  }
}