{"name": "wechaty_initializer", "version": "1.0.0", "description": "initialize multiple wechaty bots", "main": "index.js", "scripts": {"tsc-check": "tsc --noEmit", "prepare": "husky install", "prisma": "prisma generate", "server": "ts-node bot_starter/dispatcher/server.ts", "server:moer:main": "ts-node bot_starter/dispatcher/moer_event_server.ts", "server:moer:mainland": "ts-node bot_starter/dispatcher/moer_event_mainland.ts", "server:moer:overseas": "ts-node bot_starter/dispatcher/moer_event_mainland.ts", "client:syq": "export NODE_ENV=dev WECHAT_NAME=syq && ts-node bot_starter/client/client_server.ts", "client:horus": "export NODE_ENV=dev WECHAT_NAME=Horus && ts-node bot_starter/client/client_server.ts", "client:hahaha": "export NODE_ENV=dev WECHAT_NAME=HaHaHa && ts-node bot_starter/client/client_server.ts", "client:qiaoqiao": "export NODE_ENV=dev WECHAT_NAME=qiaoqiao && ts-node bot_starter/client/client_server.ts", "client:tongtong": "export NODE_ENV=dev WECHAT_NAME=tongtong && ts-node bot_starter/client/client_server.ts", "client:moer3": "export NODE_ENV=dev WECHAT_NAME=moer3 && ts-node bot_starter/client/client_server.ts", "client:moer4": "export NODE_ENV=dev WECHAT_NAME=moer4 && ts-node bot_starter/client/client_server.ts", "client:moer5": "export NODE_ENV=dev WECHAT_NAME=moer5 && ts-node bot_starter/client/client_server.ts", "client:moer6": "export NODE_ENV=dev WECHAT_NAME=moer6 && ts-node bot_starter/client/client_server.ts", "client:moer7": "export NODE_ENV=dev WECHAT_NAME=moer7 && ts-node bot_starter/client/client_server.ts", "client:moer9": "export NODE_ENV=dev WECHAT_NAME=moer9 && ts-node bot_starter/client/client_server.ts", "client": "ts-node bot_starter/client/client_server.ts", "local": "ts-node local.ts", "deploy": "ts-node docker/deploy.ts", "wecom_api": "ts-node test/wecom_api.ts"}, "author": "free spirit", "license": "ISC", "devDependencies": {"@larksuiteoapi/node-sdk": "^1.29.0", "@types/ali-oss": "^6.0.8", "@types/express": "^4.17.19", "@types/jest": "^29.5.12", "@types/jest-when": "^3.5.5", "@types/mime": "^3.0.3", "@types/node": "^20.7.0", "@types/papaparse": "^5.3.15", "@types/qrcode-terminal": "^0.12.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.30.0", "ali-oss": "^6.17.1", "csv-parse": "^4.16.3", "csv-writer": "^1.6.0", "docx": "^8.5.0", "docx-parser": "^0.2.1", "esbuild": "^0.19.4", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.0", "fuzzy": "^0.1.3", "globby": "^11.0.4", "husky": "^7.0.4", "inquirer": "^8.2.6", "inquirer-autocomplete-prompt": "^1.4.0", "jest": "^29.7.0", "jest-when": "^3.7.0", "js-yaml": "^4.1.0", "langsmith": "^0.3.29", "mammoth": "^1.8.0", "mockdate": "^3.0.5", "mongoose": "^6.12.3", "nodemon": "^3.0.1", "p-limit": "^6.1.0", "papaparse": "^5.4.1", "pdf-parse": "^1.1.1", "prettier": "^3.0.3", "prisma": "^5.8.0", "readline-sync": "^1.4.10", "ts-jest": "^29.2.4", "ts-node": "^10.9.1", "typescript": "^5.2.2", "typescript-eslint": "^8.30.1", "xlsx": "^0.18.5", "yaml": "^1.10.2", "zip-local": "^0.3.5"}, "dependencies": {"@elastic/elasticsearch": "^8.13.1", "@faker-js/faker": "^8.4.1", "@langchain/community": "^0.3.44", "@langchain/core": "^0.3.57", "@langchain/openai": "^0.5.11", "@prisma/client": "^5.8.0", "@types/express-ws": "^3.0.4", "@types/js-yaml": "^4.0.9", "alibabacloud-nls": "^1.0.2", "async-sema": "^3.1.1", "axios": "^1.5.1", "bullmq": "^5.12.9", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.12", "cron": "^3.1.7", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "express": "^4.18.2", "express-ws": "^5.0.2", "fast-xml-parser": "^4.3.5", "ioredis": "^5.4.1", "jsonrepair": "^3.6.0", "langchain": "^0.3.27", "lru-cache": "^10.2.0", "markdown-to-text": "^0.1.1", "mime": "^3.0.0", "multer": "^1.4.5-lts.1", "openai": "^4.26.0", "openai-zod-functions": "^0.1.2", "pickleparser": "^0.2.1", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "qrcode-terminal": "^0.12.0", "redlock": "^5.0.0-beta.2", "set-interval-async": "^3.0.3", "short-uuid": "^4.2.2", "silk-wasm": "^3.6.1", "string-width": "4.2.3", "xbb-api": "^0.0.4", "zod": "^3.23.8"}}